;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[1] i64 }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func {
    locals[0]
    i32.const 1
  }
}

(;; STDERR ;;;
out/test/binary/bad-typecheck-fail/bad-typecheck-fail.wasm:000001a: error: type mismatch in implicit return, expected [i64] but got [i32]
out/test/binary/bad-typecheck-fail/bad-typecheck-fail.wasm:000001a: error: type mismatch in implicit return, expected [i64] but got [i32]
;;; STDERR ;;)
