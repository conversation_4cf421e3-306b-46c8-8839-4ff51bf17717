/*
 * Copyright 2017 WebAssembly Community Group participants
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef WABT_RANGE_H_
#define WABT_RANGE_H_

namespace wabt {

template <typename T>
struct Range {
  Range() : start(0), end(0) {}
  Range(T start, T end) : start(start), end(end) {}
  T start;
  T end;

  T size() const { return end - start; }
};

typedef Range<Offset> OffsetRange;
typedef Range<int> ColumnRange;

}  // namespace wabt

#endif  // WABT_RANGE_H_
