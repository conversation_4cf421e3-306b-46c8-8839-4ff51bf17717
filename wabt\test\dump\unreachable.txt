;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    unreachable))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 00                                        ; local decl count
0000017: 00                                        ; unreachable
0000018: 0b                                        ; end
0000015: 03                                        ; FIXUP func body size
0000013: 05                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

unreachable.wasm:	file format wasm 0x1

Code Disassembly:

000016 func[0]:
 000017: 00                         | unreachable
 000018: 0b                         | end
;;; STDOUT ;;)
