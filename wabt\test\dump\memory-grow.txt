;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (memory 1 2)
  (func (param i32)
    local.get 0
    memory.grow
    drop))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 01                                        ; num params
000000d: 7f                                        ; i32
000000e: 00                                        ; num results
0000009: 05                                        ; FIXUP section size
; section "Function" (3)
000000f: 03                                        ; section code
0000010: 00                                        ; section size (guess)
0000011: 01                                        ; num functions
0000012: 00                                        ; function 0 signature index
0000010: 02                                        ; FIXUP section size
; section "Memory" (5)
0000013: 05                                        ; section code
0000014: 00                                        ; section size (guess)
0000015: 01                                        ; num memories
; memory 0
0000016: 01                                        ; limits: flags
0000017: 01                                        ; limits: initial
0000018: 02                                        ; limits: max
0000014: 04                                        ; FIXUP section size
; section "Code" (10)
0000019: 0a                                        ; section code
000001a: 00                                        ; section size (guess)
000001b: 01                                        ; num functions
; function body 0
000001c: 00                                        ; func body size (guess)
000001d: 00                                        ; local decl count
000001e: 20                                        ; local.get
000001f: 00                                        ; local index
0000020: 40                                        ; memory.grow
0000021: 00                                        ; memory.grow memidx
0000022: 1a                                        ; drop
0000023: 0b                                        ; end
000001c: 07                                        ; FIXUP func body size
000001a: 09                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

memory-grow.wasm:	file format wasm 0x1

Code Disassembly:

00001d func[0]:
 00001e: 20 00                      | local.get 0
 000020: 40 00                      | memory.grow 0
 000022: 1a                         | drop
 000023: 0b                         | end
;;; STDOUT ;;)
