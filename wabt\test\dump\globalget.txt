;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (global i32 (i32.const 0))
  (func (result i32)
    global.get 0))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 01                                        ; num results
000000e: 7f                                        ; i32
0000009: 05                                        ; FIXUP section size
; section "Function" (3)
000000f: 03                                        ; section code
0000010: 00                                        ; section size (guess)
0000011: 01                                        ; num functions
0000012: 00                                        ; function 0 signature index
0000010: 02                                        ; FIXUP section size
; section "Global" (6)
0000013: 06                                        ; section code
0000014: 00                                        ; section size (guess)
0000015: 01                                        ; num globals
0000016: 7f                                        ; i32
0000017: 00                                        ; global mutability
0000018: 41                                        ; i32.const
0000019: 00                                        ; i32 literal
000001a: 0b                                        ; end
0000014: 06                                        ; FIXUP section size
; section "Code" (10)
000001b: 0a                                        ; section code
000001c: 00                                        ; section size (guess)
000001d: 01                                        ; num functions
; function body 0
000001e: 00                                        ; func body size (guess)
000001f: 00                                        ; local decl count
0000020: 23                                        ; global.get
0000021: 00                                        ; global index
0000022: 0b                                        ; end
000001e: 04                                        ; FIXUP func body size
000001c: 06                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

globalget.wasm:	file format wasm 0x1

Code Disassembly:

00001f func[0]:
 000020: 23 00                      | global.get 0
 000022: 0b                         | end
;;; STDOUT ;;)
