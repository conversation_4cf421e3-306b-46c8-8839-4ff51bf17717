;;; TOOL: run-gen-wasm-bad
;;; ARGS1: --disable-reference-types --enable-tail-call
;;; ARGS2: --disable-reference-types --enable-tail-call
magic
version
section(TYPE) { count[1] function params[0] results[0]}
section(FUNCTION) { count[1] type[0] }
section(TABLE) { count[1] funcref flags[0] init[0] }
section(CODE) {
  count[1]
  func {
    locals[0]
    i32.const 0
    ;; The call_indirect reserved byte must be a single 0 byte. Using a long
    ;; leb128 encoding of 0 is not valid.
    return_call_indirect
      index[leb_i32(0)]
      reserved[0x80 0]
  }
}
(;; STDERR ;;;
0000022: error: return_call_indirect reserved value must be 0
0000022: error: return_call_indirect reserved value must be 0
;;; STDERR ;;)
