;;; TOOL: run-objdump

(module
  ;; i8x16 neg
  (func (export "i8x16_neg_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i8x16.neg)

  ;; i16x8 neg
  (func (export "i16x8_neg_0") (result v128)
    v128.const i32x4 0x0000ffff 0x00007fff 0x00000003 0x00000004
    i16x8.neg)

  ;; i32x4 neg
  (func (export "i32x4_neg_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i32x4.neg)

  ;; i64x2 neg
  (func (export "i64x2_neg_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i64x2.neg)

  ;; v128 not
  (func (export "v128_not_0") (result v128)
    v128.const i32x4 0x00ff0001 0x00550002 0x00000003 0x00000004
    v128.not)

  ;; v128 any_true
  (func (export "v128_any_true_0") (result i32)
    v128.const i32x4 0x00ff0001 0x00550002 0x00000003 0x00000004
    v128.any_true)

  ;; i8x16 all_true
  (func (export "i8x16_all_true_0") (result i32)
    v128.const i32x4 0x01020304 0x01050706 0x10020403 0x20103004
    i8x16.all_true)

  ;; i16x8 all_true
  (func (export "i16x8_all_true_0") (result i32)
    v128.const i32x4 0x00040004 0x00030003 0x00020002 0x00010001
    i16x8.all_true)

  ;; i32x4 all_true
  (func (export "i32x4_all_true_0") (result i32)
    v128.const i32x4 0x00ff0001 0x00550002 0x00000003 0x00000004
    i32x4.all_true)

  ;; i8x16 bitmask
  (func (export "i8x16_bitmask_0") (result i32)
    v128.const i32x4 0x00ff0001 0x00550002 0x00000003 0x00000004
    i8x16.bitmask)

  ;; i16x8 bitmask
  (func (export "i16x8_bitmask_0") (result i32)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0x00000000
    i16x8.bitmask)

  ;; i32x4 bitmask
  (func (export "i32x4_bitmask_0") (result i32)
    v128.const i32x4 0x00ff0001 0x00550002 0x00000003 0x00000004
    i32x4.bitmask)

  ;; f32x4 neg
  (func (export "f32x4_neg_0") (result v128)
    v128.const i32x4 0x80000000 0xffc00000 0x449a5000 0xbf800000
    f32x4.neg)

  ;; f64x2 neg
  (func (export "f64x2_neg_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    f64x2.neg)
  (func (export "f64x2_neg_1") (result v128)
    v128.const i32x4 0x00000000 0xc0934a00 0x00000000 0x3ff00000
    f64x2.neg)

  ;; f32x4 abs
  (func (export "f32x4_abs_0") (result v128)
    v128.const i32x4 0x80000000 0xffc00000 0x449a5000 0xbf800000
    f32x4.abs)

  ;; f64x2 abs
  (func (export "f64x2_abs_0") (result v128)
    v128.const i32x4 0x00000000 0x80000000 0x00000000 0xfff80000
    f64x2.abs)
  (func (export "f64x2_abs_1") (result v128)
    v128.const i32x4 0x00000000 0xc0934a00 0x00000000 0x3ff00000
    f64x2.abs)

  ;; f32x4 sqrt
  (func (export "f32x4_sqrt_0") (result v128)
    v128.const i32x4 0xbf800000 0xffc00000 0x40800000 0x41100000
    f32x4.sqrt)

  ;; f64x2 sqrt
  (func (export "f64x2_sqrt_0") (result v128)
    v128.const i32x4 0x00000000 0xbff00000 0x00000000 0xfff80000
    f64x2.sqrt)
  (func (export "f64x2_sqrt_1") (result v128)
    v128.const i32x4 0x00000000 0x40100000 0x00000000 0x40220000
    f64x2.sqrt)

  ;; f32x4 convert_i32x4_s
  (func (export "f32x4_convert_i32x4_s_0") (result v128)
    v128.const i32x4 0x00000001 0xffffffff 0x00000000 0x00000003
    f32x4.convert_i32x4_s)

  ;; f32x4 convert_i32x4_u
  (func (export "f32x4_convert_i32x4_u_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000000 0x00000003
    f32x4.convert_i32x4_u)

  ;; i32x4 trunc_sat_f32x4_s
  (func (export "i32x4_trunc_sat_f32x4_s_0") (result v128)
    v128.const i32x4 0x3fc00000 0xc0900000 0xffc00000 0x449a599a
    i32x4.trunc_sat_f32x4_s)

  ;; i32x4 trunc_sat_f32x4_u
  (func (export "i32x4_trunc_sat_f32x4_u_0") (result v128)
    v128.const i32x4 0x3fc00000 0x40900000 0xffc00000 0x449a599a
    i32x4.trunc_sat_f32x4_u)
)
(;; STDOUT ;;;

simd-unary.wasm:	file format wasm 0x1

Code Disassembly:

0001eb func[0] <i8x16_neg_0>:
 0001ec: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0001f5: 00 03 00 00 00 04 00 00 00 | 
 0001fe: fd 61                      | i8x16.neg
 000200: 0b                         | end
000202 func[1] <i16x8_neg_0>:
 000203: fd 0c ff ff 00 00 ff 7f 00 | v128.const 0x0000ffff 0x00007fff 0x00000003 0x00000004
 00020c: 00 03 00 00 00 04 00 00 00 | 
 000215: fd 81 01                   | i16x8.neg
 000218: 0b                         | end
00021a func[2] <i32x4_neg_0>:
 00021b: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000224: 00 03 00 00 00 04 00 00 00 | 
 00022d: fd a1 01                   | i32x4.neg
 000230: 0b                         | end
000232 func[3] <i64x2_neg_0>:
 000233: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 00023c: 00 03 00 00 00 04 00 00 00 | 
 000245: fd c1 01                   | i64x2.neg
 000248: 0b                         | end
00024a func[4] <v128_not_0>:
 00024b: fd 0c 01 00 ff 00 02 00 55 | v128.const 0x00ff0001 0x00550002 0x00000003 0x00000004
 000254: 00 03 00 00 00 04 00 00 00 | 
 00025d: fd 4d                      | v128.not
 00025f: 0b                         | end
000261 func[5] <v128_any_true_0>:
 000262: fd 0c 01 00 ff 00 02 00 55 | v128.const 0x00ff0001 0x00550002 0x00000003 0x00000004
 00026b: 00 03 00 00 00 04 00 00 00 | 
 000274: fd 53                      | v128.any_true
 000276: 0b                         | end
000278 func[6] <i8x16_all_true_0>:
 000279: fd 0c 04 03 02 01 06 07 05 | v128.const 0x01020304 0x01050706 0x10020403 0x20103004
 000282: 01 03 04 02 10 04 30 10 20 | 
 00028b: fd 63                      | i8x16.all_true
 00028d: 0b                         | end
00028f func[7] <i16x8_all_true_0>:
 000290: fd 0c 04 00 04 00 03 00 03 | v128.const 0x00040004 0x00030003 0x00020002 0x00010001
 000299: 00 02 00 02 00 01 00 01 00 | 
 0002a2: fd 83 01                   | i16x8.all_true
 0002a5: 0b                         | end
0002a7 func[8] <i32x4_all_true_0>:
 0002a8: fd 0c 01 00 ff 00 02 00 55 | v128.const 0x00ff0001 0x00550002 0x00000003 0x00000004
 0002b1: 00 03 00 00 00 04 00 00 00 | 
 0002ba: fd a3 01                   | i32x4.all_true
 0002bd: 0b                         | end
0002bf func[9] <i8x16_bitmask_0>:
 0002c0: fd 0c 01 00 ff 00 02 00 55 | v128.const 0x00ff0001 0x00550002 0x00000003 0x00000004
 0002c9: 00 03 00 00 00 04 00 00 00 | 
 0002d2: fd 64                      | i8x16.bitmask
 0002d4: 0b                         | end
0002d6 func[10] <i16x8_bitmask_0>:
 0002d7: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0x00000000
 0002e0: 00 00 00 00 00 00 00 00 00 | 
 0002e9: fd 84 01                   | i16x8.bitmask
 0002ec: 0b                         | end
0002ee func[11] <i32x4_bitmask_0>:
 0002ef: fd 0c 01 00 ff 00 02 00 55 | v128.const 0x00ff0001 0x00550002 0x00000003 0x00000004
 0002f8: 00 03 00 00 00 04 00 00 00 | 
 000301: fd a4 01                   | i32x4.bitmask
 000304: 0b                         | end
000306 func[12] <f32x4_neg_0>:
 000307: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x449a5000 0xbf800000
 000310: ff 00 50 9a 44 00 00 80 bf | 
 000319: fd e1 01                   | f32x4.neg
 00031c: 0b                         | end
00031e func[13] <f64x2_neg_0>:
 00031f: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 000328: 00 00 00 00 00 00 00 f8 ff | 
 000331: fd ed 01                   | f64x2.neg
 000334: 0b                         | end
000336 func[14] <f64x2_neg_1>:
 000337: fd 0c 00 00 00 00 00 4a 93 | v128.const 0x00000000 0xc0934a00 0x00000000 0x3ff00000
 000340: c0 00 00 00 00 00 00 f0 3f | 
 000349: fd ed 01                   | f64x2.neg
 00034c: 0b                         | end
00034e func[15] <f32x4_abs_0>:
 00034f: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x449a5000 0xbf800000
 000358: ff 00 50 9a 44 00 00 80 bf | 
 000361: fd e0 01                   | f32x4.abs
 000364: 0b                         | end
000366 func[16] <f64x2_abs_0>:
 000367: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x80000000 0x00000000 0xfff80000
 000370: 80 00 00 00 00 00 00 f8 ff | 
 000379: fd ec 01                   | f64x2.abs
 00037c: 0b                         | end
00037e func[17] <f64x2_abs_1>:
 00037f: fd 0c 00 00 00 00 00 4a 93 | v128.const 0x00000000 0xc0934a00 0x00000000 0x3ff00000
 000388: c0 00 00 00 00 00 00 f0 3f | 
 000391: fd ec 01                   | f64x2.abs
 000394: 0b                         | end
000396 func[18] <f32x4_sqrt_0>:
 000397: fd 0c 00 00 80 bf 00 00 c0 | v128.const 0xbf800000 0xffc00000 0x40800000 0x41100000
 0003a0: ff 00 00 80 40 00 00 10 41 | 
 0003a9: fd e3 01                   | f32x4.sqrt
 0003ac: 0b                         | end
0003ae func[19] <f64x2_sqrt_0>:
 0003af: fd 0c 00 00 00 00 00 00 f0 | v128.const 0x00000000 0xbff00000 0x00000000 0xfff80000
 0003b8: bf 00 00 00 00 00 00 f8 ff | 
 0003c1: fd ef 01                   | f64x2.sqrt
 0003c4: 0b                         | end
0003c6 func[20] <f64x2_sqrt_1>:
 0003c7: fd 0c 00 00 00 00 00 00 10 | v128.const 0x00000000 0x40100000 0x00000000 0x40220000
 0003d0: 40 00 00 00 00 00 00 22 40 | 
 0003d9: fd ef 01                   | f64x2.sqrt
 0003dc: 0b                         | end
0003de func[21] <f32x4_convert_i32x4_s_0>:
 0003df: fd 0c 01 00 00 00 ff ff ff | v128.const 0x00000001 0xffffffff 0x00000000 0x00000003
 0003e8: ff 00 00 00 00 03 00 00 00 | 
 0003f1: fd fa 01                   | f32x4.convert_i32x4_s
 0003f4: 0b                         | end
0003f6 func[22] <f32x4_convert_i32x4_u_0>:
 0003f7: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000000 0x00000003
 000400: 00 00 00 00 00 03 00 00 00 | 
 000409: fd fb 01                   | f32x4.convert_i32x4_u
 00040c: 0b                         | end
00040e func[23] <i32x4_trunc_sat_f32x4_s_0>:
 00040f: fd 0c 00 00 c0 3f 00 00 90 | v128.const 0x3fc00000 0xc0900000 0xffc00000 0x449a599a
 000418: c0 00 00 c0 ff 9a 59 9a 44 | 
 000421: fd f8 01                   | i32x4.trunc_sat_f32x4_s
 000424: 0b                         | end
000426 func[24] <i32x4_trunc_sat_f32x4_u_0>:
 000427: fd 0c 00 00 c0 3f 00 00 90 | v128.const 0x3fc00000 0x40900000 0xffc00000 0x449a599a
 000430: 40 00 00 c0 ff 9a 59 9a 44 | 
 000439: fd f9 01                   | i32x4.trunc_sat_f32x4_u
 00043c: 0b                         | end
;;; STDOUT ;;)
