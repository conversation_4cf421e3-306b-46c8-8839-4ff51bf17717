;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (type $t (func (param i32)))
  (func $f (type $t)
    i32.const 0
    i32.const 0
    call_indirect (type $t))
  (table funcref (elem $f)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 01                                        ; num params
000000d: 7f                                        ; i32
000000e: 00                                        ; num results
0000009: 05                                        ; FIXUP section size
; section "Function" (3)
000000f: 03                                        ; section code
0000010: 00                                        ; section size (guess)
0000011: 01                                        ; num functions
0000012: 00                                        ; function 0 signature index
0000010: 02                                        ; FIXUP section size
; section "Table" (4)
0000013: 04                                        ; section code
0000014: 00                                        ; section size (guess)
0000015: 01                                        ; num tables
; table 0
0000016: 70                                        ; funcref
0000017: 01                                        ; limits: flags
0000018: 01                                        ; limits: initial
0000019: 01                                        ; limits: max
0000014: 05                                        ; FIXUP section size
; section "Elem" (9)
000001a: 09                                        ; section code
000001b: 00                                        ; section size (guess)
000001c: 01                                        ; num elem segments
; elem segment header 0
000001d: 00                                        ; segment flags
000001e: 41                                        ; i32.const
000001f: 00                                        ; i32 literal
0000020: 0b                                        ; end
0000021: 01                                        ; num elems
0000022: 00                                        ; elem function index
000001b: 07                                        ; FIXUP section size
; section "Code" (10)
0000023: 0a                                        ; section code
0000024: 00                                        ; section size (guess)
0000025: 01                                        ; num functions
; function body 0
0000026: 00                                        ; func body size (guess)
0000027: 00                                        ; local decl count
0000028: 41                                        ; i32.const
0000029: 00                                        ; i32 literal
000002a: 41                                        ; i32.const
000002b: 00                                        ; i32 literal
000002c: 11                                        ; call_indirect
000002d: 00                                        ; signature index
000002e: 00                                        ; table index
000002f: 0b                                        ; end
0000026: 09                                        ; FIXUP func body size
0000024: 0b                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

callindirect.wasm:	file format wasm 0x1

Code Disassembly:

000027 func[0]:
 000028: 41 00                      | i32.const 0
 00002a: 41 00                      | i32.const 0
 00002c: 11 00 00                   | call_indirect 0 (type 0)
 00002f: 0b                         | end
;;; STDOUT ;;)
