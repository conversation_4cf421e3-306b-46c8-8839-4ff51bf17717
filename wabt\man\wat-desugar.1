.Dd $Mdocdate$
.Dt WABT 1
.Os
.Sh NAME
.Nm wat-desugar
.Nd parse .wat text form and print "canonical" flat format
.Sh SYNOPSIS
.Nm wat-desugar
.Op options
.Ar file
.Sh DESCRIPTION
.Nm
parses .wat text form as supported by the spec interpreter (s-expressions, flat syntax, or mixed) and prints "canonical" flat format.
.Pp
The options are as follows:
.Bl -tag -width Ds
.It Fl Fl help
Print a help message
.It Fl o , Fl Fl output=FILE
Output file for the formatted file
.It Fl Fl debug-parser
Turn on debugging the parser of wat files
.It Fl f , Fl Fl fold-exprs
Write folded expressions where possible
.It Fl Fl enable-exceptions
Experimental exception handling
.It Fl Fl disable-mutable-globals
Import/export mutable globals
.It Fl Fl enable-saturating-float-to-int
Saturating float-to-int operators
.It Fl Fl enable-sign-extension
Sign-extension operators
.It Fl Fl disable-simd
SIMD support
.It Fl Fl enable-threads
Threading support
.It Fl Fl inline-exports
Write all exports inline
.It Fl Fl inline-imports
Write all imports inline
.It Fl Fl generate-names
Give auto-generated names to non-named functions, types, etc.
.El
.Sh EXAMPLES
Write output to stdout
.Pp
.Dl $ wat-desugar test.wat
.Pp
Write output to test2.wat
.Pp
.Dl $ wat-desugar test.wat -o test2.wat
.Pp
Generate names for indexed variables
.Pp
.Dl $ wat-desugar --generate-names test.wat
.Sh SEE ALSO
.Xr wasm-interp 1 ,
.Xr wasm-objdump 1 ,
.Xr wasm-opcodecnt 1 ,
.Xr wasm-strip 1 ,
.Xr wasm-validate 1 ,
.Xr wasm2c 1 ,
.Xr wasm2wat 1 ,
.Xr wast2json 1 ,
.Xr wat2wasm 1,
.Xr spectest-interp 1
.Sh BUGS
If you find a bug, please report it at
.br
.Lk https://github.com/WebAssembly/wabt/issues .
