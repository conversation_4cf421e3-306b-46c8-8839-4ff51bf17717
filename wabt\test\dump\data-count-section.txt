;;; TOOL: run-objdump
;;; ARGS1: -hx
(module
  (memory 1)
  (data "hi")

  ;; include an instruction that requires the DataCount section, otherwise it
  ;; will be removed.
  (func data.drop 0)
)
(;; STDOUT ;;;

data-count-section.wasm:	file format wasm 0x1

Sections:

     Type start=0x0000000a end=0x0000000e (size=0x00000004) count: 1
 Function start=0x00000010 end=0x00000012 (size=0x00000002) count: 1
   Memory start=0x00000014 end=0x00000017 (size=0x00000003) count: 1
DataCount start=0x00000019 end=0x0000001a (size=0x00000001) count: 1
     Code start=0x0000001c end=0x00000023 (size=0x00000007) count: 1
     Data start=0x00000025 end=0x0000002a (size=0x00000005) count: 1

Section Details:

Type[1]:
 - type[0] () -> nil
Function[1]:
 - func[0] sig=0
Memory[1]:
 - memory[0] pages: initial=1
DataCount:
 - data count: 1
Code[1]:
 - func[0] size=5
Data[1]:
 - segment[0] passive size=2
  - 0000000: 6869                                     hi

Code Disassembly:

00001e func[0]:
 00001f: fc 09 00                   | data.drop 0
 000022: 0b                         | end
;;; STDOUT ;;)
