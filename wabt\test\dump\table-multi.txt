;;; TOOL: run-objdump
;;; ARGS0: -v
;;; ARGS1: -x
(module
  (func (param i32))
  (table funcref (elem 0))
  (table funcref (elem 0)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 01                                        ; num params
000000d: 7f                                        ; i32
000000e: 00                                        ; num results
0000009: 05                                        ; FIXUP section size
; section "Function" (3)
000000f: 03                                        ; section code
0000010: 00                                        ; section size (guess)
0000011: 01                                        ; num functions
0000012: 00                                        ; function 0 signature index
0000010: 02                                        ; FIXUP section size
; section "Table" (4)
0000013: 04                                        ; section code
0000014: 00                                        ; section size (guess)
0000015: 02                                        ; num tables
; table 0
0000016: 70                                        ; funcref
0000017: 01                                        ; limits: flags
0000018: 01                                        ; limits: initial
0000019: 01                                        ; limits: max
; table 1
000001a: 70                                        ; funcref
000001b: 01                                        ; limits: flags
000001c: 01                                        ; limits: initial
000001d: 01                                        ; limits: max
0000014: 09                                        ; FIXUP section size
; section "Elem" (9)
000001e: 09                                        ; section code
000001f: 00                                        ; section size (guess)
0000020: 02                                        ; num elem segments
; elem segment header 0
0000021: 00                                        ; segment flags
0000022: 41                                        ; i32.const
0000023: 00                                        ; i32 literal
0000024: 0b                                        ; end
0000025: 01                                        ; num elems
0000026: 00                                        ; elem function index
; elem segment header 1
0000027: 02                                        ; segment flags
0000028: 01                                        ; table index
0000029: 41                                        ; i32.const
000002a: 00                                        ; i32 literal
000002b: 0b                                        ; end
000002c: 00                                        ; elem list type
000002d: 01                                        ; num elems
000002e: 00                                        ; elem function index
000001f: 0f                                        ; FIXUP section size
; section "Code" (10)
000002f: 0a                                        ; section code
0000030: 00                                        ; section size (guess)
0000031: 01                                        ; num functions
; function body 0
0000032: 00                                        ; func body size (guess)
0000033: 00                                        ; local decl count
0000034: 0b                                        ; end
0000032: 02                                        ; FIXUP func body size
0000030: 04                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

table-multi.wasm:	file format wasm 0x1

Section Details:

Type[1]:
 - type[0] (i32) -> nil
Function[1]:
 - func[0] sig=0
Table[2]:
 - table[0] type=funcref initial=1 max=1
 - table[1] type=funcref initial=1 max=1
Elem[2]:
 - segment[0] flags=0 table=0 count=1 - init i32=0
  - elem[0] = func[0]
 - segment[1] flags=2 table=1 count=1 - init i32=0
  - elem[0] = func[0]
Code[1]:
 - func[0] size=2

Code Disassembly:

000033 func[0]:
 000034: 0b                         | end
;;; STDOUT ;;)
