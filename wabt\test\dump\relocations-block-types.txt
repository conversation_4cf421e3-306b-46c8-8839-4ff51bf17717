;;; TOOL: run-objdump
;;; ARGS0: -r
;;; ARGS1: -x
(module
  (func $multivalue_block (param i32) (result i32 i32)
    (if (result i32 i32)
        (local.get 0)
        (then
            i32.const 0
            i32.const 1)
        (else
            i32.const 2
            i32.const 3)))
  (export "multivalue_block" (func $multivalue_block)))
(;; STDOUT ;;;

relocations-block-types.wasm:	file format wasm 0x1

Section Details:

Type[2]:
 - type[0] (i32) -> (i32, i32)
 - type[1] () -> (i32, i32)
Function[1]:
 - func[0] sig=0 <multivalue_block>
Export[1]:
 - func[0] <multivalue_block> -> "multivalue_block"
Code[1]:
 - func[0] size=20 <multivalue_block>
Custom:
 - name: "linking"
  - symbol table [count=1]
   - 0: F <multivalue_block> func=0 [ exported no_strip binding=global vis=hidden ]
Custom:
 - name: "reloc.Code"
  - relocations for section: 3 (Code) [1]
   - R_WASM_TYPE_INDEX_LEB offset=0x000006(file=0x000038) type=1

Code Disassembly:

000034 func[0] <multivalue_block>:
 000035: 20 00                      | local.get 0
 000037: 04 81 80 80 80 00          | if type[1]
           000038: R_WASM_TYPE_INDEX_LEB 1
 00003d: 41 00                      |   i32.const 0
 00003f: 41 01                      |   i32.const 1
 000041: 05                         | else
 000042: 41 02                      |   i32.const 2
 000044: 41 03                      |   i32.const 3
 000046: 0b                         | end
 000047: 0b                         | end
;;; STDOUT ;;)
