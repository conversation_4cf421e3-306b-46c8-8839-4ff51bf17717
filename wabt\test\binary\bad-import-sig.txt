;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(IMPORT) { count[1] str("module") str("func") func_kind type[1] }
(;; STDERR ;;;
out/test/binary/bad-import-sig/bad-import-sig.wasm:0000020: error: function type variable out of range: 1 (max 1)
out/test/binary/bad-import-sig/bad-import-sig.wasm:0000020: error: function type variable out of range: 1 (max 1)
;;; STDERR ;;)
