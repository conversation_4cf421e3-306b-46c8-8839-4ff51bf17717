;;; TOOL: run-objdump
;;; ARGS0: -r --enable-all
;;; ARGS1: -x
(module
  (type (;0;) (func))
  (import "env" "b" (func (;0;) (type 0)))
  (func $a (type 0)
    call 0)
  (func (type 0)
    call 0)
  (func $b (type 0)
    (call_indirect (type 0) (i32.const 0))
    call 0)
  (export "a" (func $a))
  (table $t 0 funcref))
(;; STDOUT ;;;

symbol-tables-all-features.wasm:	file format wasm 0x1

Section Details:

Type[1]:
 - type[0] () -> nil
Import[1]:
 - func[0] sig=0 <env.b> <- env.b
Function[3]:
 - func[1] sig=0 <a>
 - func[2] sig=0
 - func[3] sig=0 <b>
Table[1]:
 - table[0] type=funcref initial=0 <t>
Export[1]:
 - func[1] <a> -> "a"
Code[3]:
 - func[1] size=8 <a>
 - func[2] size=8
 - func[3] size=21 <b>
Custom:
 - name: "linking"
  - symbol table [count=5]
   - 0: F <env.b> func=0 [ undefined binding=global vis=default ]
   - 1: F <a> func=1 [ exported no_strip binding=global vis=hidden ]
   - 2: F <> func=2 [ binding=local vis=hidden ]
   - 3: F <b> func=3 [ binding=global vis=default ]
   - 4: T <t> table=0 [ binding=global vis=default ]
Custom:
 - name: "reloc.Code"
  - relocations for section: 5 (Code) [5]
   - R_WASM_FUNCTION_INDEX_LEB offset=0x000004(file=0x000032) symbol=0 <env.b>
   - R_WASM_FUNCTION_INDEX_LEB offset=0x00000d(file=0x00003b) symbol=0 <env.b>
   - R_WASM_TYPE_INDEX_LEB offset=0x000018(file=0x000046) type=0
   - R_WASM_TABLE_NUMBER_LEB offset=0x00001d(file=0x00004b) symbol=4 <t>
   - R_WASM_FUNCTION_INDEX_LEB offset=0x000023(file=0x000051) symbol=0 <env.b>

Code Disassembly:

000030 func[1] <a>:
 000031: 10 80 80 80 80 00          | call 0 <env.b>
           000032: R_WASM_FUNCTION_INDEX_LEB 0 <env.b>
 000037: 0b                         | end
000039 func[2]:
 00003a: 10 80 80 80 80 00          | call 0 <env.b>
           00003b: R_WASM_FUNCTION_INDEX_LEB 0 <env.b>
 000040: 0b                         | end
000042 func[3] <b>:
 000043: 41 00                      | i32.const 0
 000045: 11 80 80 80 80 00 80 80 80 | call_indirect 0 <t> (type 0)
 00004e: 80 00                      | 
           000046: R_WASM_TYPE_INDEX_LEB 0
 000050: 10 80 80 80 80 00          | call 0 <env.b>
           00004b: R_WASM_TABLE_NUMBER_LEB 4 <t>
 000056: 0b                         | end
           000051: R_WASM_FUNCTION_INDEX_LEB 0 <env.b>
;;; STDOUT ;;)
