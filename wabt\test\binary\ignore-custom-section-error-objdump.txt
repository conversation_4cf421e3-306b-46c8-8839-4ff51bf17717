;;; TOOL: run-objdump-gen-wasm
;;; ARGS: -x
magic
version
section("linking") {
  metadata_version[2]
  2 3 4 5 6 7
}

;; Some dummy data to make sure the module is still parsed after the invalid
;; linking section.
section(TYPE) { count[1] function params[0] results[0] }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func {
    locals[0]
    return
  }
}
(;; STDERR ;;;
0000019: warning: unable to read u32 leb128: subsection size
;;; STDERR ;;)
(;; STDOUT ;;;

ignore-custom-section-error-objdump.wasm:	file format wasm 0x1

Section Details:

Custom:
 - name: "linking"
Type[1]:
 - type[0] () -> nil
Function[1]:
 - func[0] sig=0
Code[1]:
 - func[0] size=3

Code Disassembly:

000027 func[0]:
 000028: 0f                         | return
 000029: 0b                         | end
;;; STDOUT ;;)
