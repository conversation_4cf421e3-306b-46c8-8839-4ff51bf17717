;;; TOOL: run-objdump
;;; ARGS0: -v
;;; ARGS1: -x
(module
  (import "foo" "i32_global" (global i32))
  (import "foo" "i64_global" (global i64))
  (import "foo" "f32_global" (global f32))
  (import "foo" "f64_global" (global f64))

  (global i32 (i32.const 1))
  (global i64 (i64.const 2))
  (global f32 (f32.const 3))
  (global f64 (f64.const 4))

  (global i32 (global.get 0))
  (global i64 (global.get 1))
  (global f32 (global.get 2))
  (global f64 (global.get 3)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Import" (2)
0000008: 02                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 04                                        ; num imports
; import header 0
000000b: 03                                        ; string length
000000c: 666f 6f                                  foo  ; import module name
000000f: 0a                                        ; string length
0000010: 6933 325f 676c 6f62 616c                 i32_global  ; import field name
000001a: 03                                        ; import kind
000001b: 7f                                        ; i32
000001c: 00                                        ; global mutability
; import header 1
000001d: 03                                        ; string length
000001e: 666f 6f                                  foo  ; import module name
0000021: 0a                                        ; string length
0000022: 6936 345f 676c 6f62 616c                 i64_global  ; import field name
000002c: 03                                        ; import kind
000002d: 7e                                        ; i64
000002e: 00                                        ; global mutability
; import header 2
000002f: 03                                        ; string length
0000030: 666f 6f                                  foo  ; import module name
0000033: 0a                                        ; string length
0000034: 6633 325f 676c 6f62 616c                 f32_global  ; import field name
000003e: 03                                        ; import kind
000003f: 7d                                        ; f32
0000040: 00                                        ; global mutability
; import header 3
0000041: 03                                        ; string length
0000042: 666f 6f                                  foo  ; import module name
0000045: 0a                                        ; string length
0000046: 6636 345f 676c 6f62 616c                 f64_global  ; import field name
0000050: 03                                        ; import kind
0000051: 7c                                        ; f64
0000052: 00                                        ; global mutability
0000009: 49                                        ; FIXUP section size
; section "Global" (6)
0000053: 06                                        ; section code
0000054: 00                                        ; section size (guess)
0000055: 08                                        ; num globals
0000056: 7f                                        ; i32
0000057: 00                                        ; global mutability
0000058: 41                                        ; i32.const
0000059: 01                                        ; i32 literal
000005a: 0b                                        ; end
000005b: 7e                                        ; i64
000005c: 00                                        ; global mutability
000005d: 42                                        ; i64.const
000005e: 02                                        ; i64 literal
000005f: 0b                                        ; end
0000060: 7d                                        ; f32
0000061: 00                                        ; global mutability
0000062: 43                                        ; f32.const
0000063: 0000 4040                                 ; f32 literal
0000067: 0b                                        ; end
0000068: 7c                                        ; f64
0000069: 00                                        ; global mutability
000006a: 44                                        ; f64.const
000006b: 0000 0000 0000 1040                       ; f64 literal
0000073: 0b                                        ; end
0000074: 7f                                        ; i32
0000075: 00                                        ; global mutability
0000076: 23                                        ; global.get
0000077: 00                                        ; global index
0000078: 0b                                        ; end
0000079: 7e                                        ; i64
000007a: 00                                        ; global mutability
000007b: 23                                        ; global.get
000007c: 01                                        ; global index
000007d: 0b                                        ; end
000007e: 7d                                        ; f32
000007f: 00                                        ; global mutability
0000080: 23                                        ; global.get
0000081: 02                                        ; global index
0000082: 0b                                        ; end
0000083: 7c                                        ; f64
0000084: 00                                        ; global mutability
0000085: 23                                        ; global.get
0000086: 03                                        ; global index
0000087: 0b                                        ; end
0000054: 33                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

global.wasm:	file format wasm 0x1

Section Details:

Import[4]:
 - global[0] i32 mutable=0 <- foo.i32_global
 - global[1] i64 mutable=0 <- foo.i64_global
 - global[2] f32 mutable=0 <- foo.f32_global
 - global[3] f64 mutable=0 <- foo.f64_global
Global[8]:
 - global[4] i32 mutable=0 - init i32=1
 - global[5] i64 mutable=0 - init i64=2
 - global[6] f32 mutable=0 - init f32=0x1.8p+1
 - global[7] f64 mutable=0 - init f64=0x1p+2
 - global[8] i32 mutable=0 - init global=0 <foo.i32_global>
 - global[9] i64 mutable=0 - init global=1 <foo.i64_global>
 - global[10] f32 mutable=0 - init global=2 <foo.f32_global>
 - global[11] f64 mutable=0 - init global=3 <foo.f64_global>

Code Disassembly:

;;; STDOUT ;;)
