;;; TOOL: wat-desugar
(module
  (import "foo" "bar" (func (result i32)))

  (global i32 (i32.const 1))

  (table funcref (elem 0))

  (memory (data "hello"))

  (func (result i32)
    (i32.add (call 0) (i32.load8_s (i32.const 1)))))
(;; STDOUT ;;;
(module
  (import "foo" "bar" (func (;0;) (result i32)))
  (global (;0;) i32 (i32.const 1))
  (table (;0;) 1 1 funcref)
  (elem (;0;) (i32.const 0) func 0)
  (memory (;0;) 1 1)
  (data (;0;) (i32.const 0) "hello")
  (func (;1;) (result i32)
    call 0
    i32.const 1
    i32.load8_s
    i32.add)
  (type (;0;) (func (result i32))))
;;; STDOUT ;;)
