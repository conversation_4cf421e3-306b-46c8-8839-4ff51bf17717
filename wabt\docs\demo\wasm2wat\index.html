<!--
 Copyright 2017 WebAssembly Community Group participants

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, minimum-scale=1.0, initial-scale=1.0, user-scalable=yes">
  <title>wasm2wat demo</title>
  <link href="../third_party/codemirror/codemirror.css" rel="stylesheet">
  <link href="../custom.css" rel="stylesheet">
</head>
<body>
  <header>
    <h1>wasm2wat demo</h1>
    <p>WebAssembly has a
      <a href="https://webassembly.github.io/spec/core/text/">text format</a>
      and a
      <a href="https://webassembly.github.io/spec/core/binary/">binary format</a>.
      This demo converts from the binary format to the text format.
    </p>
    <p>
      Upload a WebAssembly binary file, and the text format will be displayed.
    </p>
    <div>

      <p>
      </p>
    </div>
    <div>Enabled features:</div>
    <div>
      <input type="checkbox" id="exceptions"><label for="exceptions">exceptions</label>
      <input type="checkbox" id="mutable_globals" checked><label for="mutable_globals">mutable globals</label>
      <input type="checkbox" id="sat_float_to_int"><label for="sat_float_to_int">saturating float to int</label>
      <input type="checkbox" id="sign_extension"><label for="sign_extension">sign extension</label>
    </div>
    <div>
      <input type="checkbox" id="simd"><label for="simd">simd</label>
      <input type="checkbox" id="threads"><label for="threads">threads</label>
      <input type="checkbox" id="multi_value"><label for="multi_value">multi value</label>
      <input type="checkbox" id="tail_call"><label for="tail_call">tail call</label>
      <input type="checkbox" id="bulk_memory"><label for="bulk_memory">bulk memory</label>
      <input type="checkbox" id="reference_types"><label for="reference_types">reference types</label>
    </div>
  </header>
  <main>
    <div class="toolbar">
      <input type="checkbox" id="generateNames" checked>
      <label for="generateNames">Generate Names</label>

      <input type="checkbox" id="foldExprs" checked>
      <label for="foldExprs">Fold Expressions</label>

      <input type="checkbox" id="inlineExport" checked>
      <label for="inlineExport">Inline Export</label>

      <input type="checkbox" id="readDebugNames" checked>
      <label for="readDebugNames">Read Debug Names</label>
      <div class="right">
        <input type="file" id="uploadInput" class="hidden"></a>
        <label>example:</label>
        <select id="select" class="form-select"></select>
        <button class="btn" type="button" id="upload">Upload</button>
      </div>
    </div>
    <textarea class="editor" autofocus autocomplete="off" autocorrect="off"
        autocapitalize="off" spellcheck="false" hidden></textarea>
  </main>
  <script src="../third_party/codemirror/codemirror.js"></script>
  <script src="../third_party/codemirror/simple-mode.js"></script>
  <script src="../wast-mode.js"></script>
  <script src="../libwabt.js"></script>
  <script src="examples.js"></script>
  <script src="demo.js"></script>
</body>
</html>
