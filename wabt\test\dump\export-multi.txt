;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func (nop))
  (export "a" (func 0))
  (export "b" (func 0)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Export" (7)
0000012: 07                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 02                                        ; num exports
0000015: 01                                        ; string length
0000016: 61                                       a  ; export name
0000017: 00                                        ; export kind
0000018: 00                                        ; export func index
0000019: 01                                        ; string length
000001a: 62                                       b  ; export name
000001b: 00                                        ; export kind
000001c: 00                                        ; export func index
0000013: 09                                        ; FIXUP section size
; section "Code" (10)
000001d: 0a                                        ; section code
000001e: 00                                        ; section size (guess)
000001f: 01                                        ; num functions
; function body 0
0000020: 00                                        ; func body size (guess)
0000021: 00                                        ; local decl count
0000022: 01                                        ; nop
0000023: 0b                                        ; end
0000020: 03                                        ; FIXUP func body size
000001e: 05                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

export-multi.wasm:	file format wasm 0x1

Code Disassembly:

000021 func[0] <b>:
 000022: 01                         | nop
 000023: 0b                         | end
;;; STDOUT ;;)
