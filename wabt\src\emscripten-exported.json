["_free", "_malloc", "_wabt_apply_names_module", "_wabt_bulk_memory_enabled", "_wabt_destroy_errors", "_wabt_destroy_features", "_wabt_destroy_module", "_wabt_destroy_output_buffer", "_wabt_destroy_parse_wat_result", "_wabt_destroy_read_binary_result", "_wabt_destroy_wast_lexer", "_wabt_destroy_write_module_result", "_wabt_exceptions_enabled", "_wabt_format_binary_errors", "_wabt_format_text_errors", "_wabt_generate_names_module", "_wabt_multi_value_enabled", "_wabt_mutable_globals_enabled", "_wabt_new_errors", "_wabt_new_features", "_wabt_new_wast_buffer_lexer", "_wabt_output_buffer_get_data", "_wabt_output_buffer_get_size", "_wabt_parse_wast", "_wabt_parse_wast_result_get_result", "_wabt_parse_wast_result_release_module", "_wabt_parse_wat", "_wabt_parse_wat_result_get_result", "_wabt_parse_wat_result_release_module", "_wabt_read_binary", "_wabt_read_binary_result_get_result", "_wabt_read_binary_result_release_module", "_wabt_reference_types_enabled", "_wabt_sat_float_to_int_enabled", "_wabt_set_bulk_memory_enabled", "_wabt_set_exceptions_enabled", "_wabt_set_multi_value_enabled", "_wabt_set_mutable_globals_enabled", "_wabt_set_reference_types_enabled", "_wabt_set_sat_float_to_int_enabled", "_wabt_set_sign_extension_enabled", "_wabt_set_simd_enabled", "_wabt_set_tail_call_enabled", "_wabt_set_threads_enabled", "_wabt_sign_extension_enabled", "_wabt_simd_enabled", "_wabt_tail_call_enabled", "_wabt_threads_enabled", "_wabt_validate_module", "_wabt_validate_script", "_wabt_write_binary_module", "_wabt_write_binary_spec_script", "_wabt_write_module_result_get_result", "_wabt_write_module_result_release_log_output_buffer", "_wabt_write_module_result_release_output_buffer", "_wabt_write_text_module", "_dummy_workaround_for_emscripten_issue_7073"]