.Dd $Mdocdate$
.Dt WABT 1
.Os
.Sh NAME
.Nm wasm-interp
.Nd decode and run a WebAssembly binary file
.Sh SYNOPSIS
.Nm wasm-interp
.Op options
.Ar file
.Sh DESCRIPTION
.Nm
decodes and runs a WebAssembly binary file using a stack-based interpreter.
.Pp
The options are as follows:
.Bl -tag -width Ds
.It Fl v , Fl Fl verbose
Use multiple times for more info
.It Fl Fl help
Print a help message
.It Fl Fl enable-exceptions
Experimental exception handling
.It Fl Fl disable-mutable-globals
Import/export mutable globals
.It Fl Fl enable-saturating-float-to-int
Saturating float-to-int operators
.It Fl Fl enable-sign-extension
Sign-extension operators
.It Fl Fl disable-simd
SIMD support
.It Fl Fl enable-threads
Threading support
.It Fl V , Fl Fl value-stack-size=SIZE
Size in elements of the value stack
.It Fl C , Fl Fl call-stack-size=SIZE
Size in elements of the call stack
.It Fl t , Fl Fl trace
Trace execution
.It Fl Fl run-all-exports
Run all the exported functions, in order. Useful for testing
.It Fl Fl host-print
Include an importable function named "host.print" for printing to stdout
.El
.Sh EXAMPLES
Parse binary file test.wasm, and type-check it
.Pp
.Dl $ wasm-interp test.wasm
.Pp
Parse test.wasm and run all its exported functions
.Pp
.Dl $ wasm-interp test.wasm --run-all-exports
.Pp
Parse test.wasm, run the exported functions and trace the output
.Pp
.Dl $ wasm-interp test.wasm --run-all-exports --trace
.Pp
Parse test.wasm and run all its exported functions, setting the value stack size to 100 elements
.Pp
.Dl $ wasm-interp test.wasm -V 100 --run-all-exports
.Sh SEE ALSO
.Xr wasm-objdump 1 ,
.Xr wasm-opcodecnt 1 ,
.Xr wasm-strip 1 ,
.Xr wasm-validate 1 ,
.Xr wasm2c 1 ,
.Xr wasm2wat 1 ,
.Xr wast2json 1 ,
.Xr wat-desugar 1 ,
.Xr wat2wasm 1 ,
.Xr spectest-interp 1
.Sh BUGS
If you find a bug, please report it at
.br
.Lk https://github.com/WebAssembly/wabt/issues .
