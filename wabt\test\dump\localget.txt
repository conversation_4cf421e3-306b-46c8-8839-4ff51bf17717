;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    (local f64 f32 i64 i32 i32 f32 f64 i64)
    local.get 0
    drop
    local.get 1
    drop
    local.get 2
    drop
    local.get 3
    drop
    local.get 4
    drop
    local.get 5
    drop
    local.get 6
    drop
    local.get 7
    drop))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 07                                        ; local decl count
0000017: 01                                        ; local type count
0000018: 7c                                        ; f64
0000019: 01                                        ; local type count
000001a: 7d                                        ; f32
000001b: 01                                        ; local type count
000001c: 7e                                        ; i64
000001d: 02                                        ; local type count
000001e: 7f                                        ; i32
000001f: 01                                        ; local type count
0000020: 7d                                        ; f32
0000021: 01                                        ; local type count
0000022: 7c                                        ; f64
0000023: 01                                        ; local type count
0000024: 7e                                        ; i64
0000025: 20                                        ; local.get
0000026: 00                                        ; local index
0000027: 1a                                        ; drop
0000028: 20                                        ; local.get
0000029: 01                                        ; local index
000002a: 1a                                        ; drop
000002b: 20                                        ; local.get
000002c: 02                                        ; local index
000002d: 1a                                        ; drop
000002e: 20                                        ; local.get
000002f: 03                                        ; local index
0000030: 1a                                        ; drop
0000031: 20                                        ; local.get
0000032: 04                                        ; local index
0000033: 1a                                        ; drop
0000034: 20                                        ; local.get
0000035: 05                                        ; local index
0000036: 1a                                        ; drop
0000037: 20                                        ; local.get
0000038: 06                                        ; local index
0000039: 1a                                        ; drop
000003a: 20                                        ; local.get
000003b: 07                                        ; local index
000003c: 1a                                        ; drop
000003d: 0b                                        ; end
0000015: 28                                        ; FIXUP func body size
0000013: 2a                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

localget.wasm:	file format wasm 0x1

Code Disassembly:

000016 func[0]:
 000017: 01 7c                      | local[0] type=f64
 000019: 01 7d                      | local[1] type=f32
 00001b: 01 7e                      | local[2] type=i64
 00001d: 02 7f                      | local[3..4] type=i32
 00001f: 01 7d                      | local[5] type=f32
 000021: 01 7c                      | local[6] type=f64
 000023: 01 7e                      | local[7] type=i64
 000025: 20 00                      | local.get 0
 000027: 1a                         | drop
 000028: 20 01                      | local.get 1
 00002a: 1a                         | drop
 00002b: 20 02                      | local.get 2
 00002d: 1a                         | drop
 00002e: 20 03                      | local.get 3
 000030: 1a                         | drop
 000031: 20 04                      | local.get 4
 000033: 1a                         | drop
 000034: 20 05                      | local.get 5
 000036: 1a                         | drop
 000037: 20 06                      | local.get 6
 000039: 1a                         | drop
 00003a: 20 07                      | local.get 7
 00003c: 1a                         | drop
 00003d: 0b                         | end
;;; STDOUT ;;)
