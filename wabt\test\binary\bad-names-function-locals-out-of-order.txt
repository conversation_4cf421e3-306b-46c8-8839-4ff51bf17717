;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(FUNCTION) { count[2] type[0] type[0] }
section(CODE) {
  count[2]
  func { locals[decl_count[1] i32_count[2] i32] }
  func { locals[decl_count[1] i32_count[2] i32] }
}
section("name") {
  section(NAME_FUNCTION) {
    func_count[2]
    index[0]
    str("F0")
    index[1]
    str("F2")
  }

  section(NAME_LOCALS) {
    func_count[2]
    func_index[1]
    local_count[1]
    local_index[0]
    str("L0")

    func_index[0]
    local_count[1]
    local_index[0]
    str("L0")
  }
}
(;; STDERR ;;;
000003d: error: locals function index out of order: 0
000003d: error: locals function index out of order: 0
;;; STDERR ;;)
