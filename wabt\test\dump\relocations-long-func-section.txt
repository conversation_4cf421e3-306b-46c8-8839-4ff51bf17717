;;; TOOL: run-objdump
;;; ARGS0: -r
;;; ARGS1:
(module
    (global $a i32 (i32.const 0))
    (func $0
        (drop (global.get $a))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0)))
    (func $1
        (drop (global.get $a))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))
        (drop (i32.const 0))))
(;; STDOUT ;;;

relocations-long-func-section.wasm:	file format wasm 0x1

Code Disassembly:

000020 func[0] <0>:
 000021: 23 80 80 80 80 00          | global.get 0 <a>
           000022: R_WASM_GLOBAL_INDEX_LEB 2 <a>
 000027: 1a                         | drop
 000028: 41 00                      | i32.const 0
 00002a: 1a                         | drop
 00002b: 41 00                      | i32.const 0
 00002d: 1a                         | drop
 00002e: 41 00                      | i32.const 0
 000030: 1a                         | drop
 000031: 41 00                      | i32.const 0
 000033: 1a                         | drop
 000034: 41 00                      | i32.const 0
 000036: 1a                         | drop
 000037: 41 00                      | i32.const 0
 000039: 1a                         | drop
 00003a: 41 00                      | i32.const 0
 00003c: 1a                         | drop
 00003d: 41 00                      | i32.const 0
 00003f: 1a                         | drop
 000040: 41 00                      | i32.const 0
 000042: 1a                         | drop
 000043: 41 00                      | i32.const 0
 000045: 1a                         | drop
 000046: 41 00                      | i32.const 0
 000048: 1a                         | drop
 000049: 41 00                      | i32.const 0
 00004b: 1a                         | drop
 00004c: 41 00                      | i32.const 0
 00004e: 1a                         | drop
 00004f: 41 00                      | i32.const 0
 000051: 1a                         | drop
 000052: 41 00                      | i32.const 0
 000054: 1a                         | drop
 000055: 41 00                      | i32.const 0
 000057: 1a                         | drop
 000058: 41 00                      | i32.const 0
 00005a: 1a                         | drop
 00005b: 41 00                      | i32.const 0
 00005d: 1a                         | drop
 00005e: 41 00                      | i32.const 0
 000060: 1a                         | drop
 000061: 41 00                      | i32.const 0
 000063: 1a                         | drop
 000064: 41 00                      | i32.const 0
 000066: 1a                         | drop
 000067: 0b                         | end
000069 func[1] <1>:
 00006a: 23 80 80 80 80 00          | global.get 0 <a>
           00006b: R_WASM_GLOBAL_INDEX_LEB 2 <a>
 000070: 1a                         | drop
 000071: 41 00                      | i32.const 0
 000073: 1a                         | drop
 000074: 41 00                      | i32.const 0
 000076: 1a                         | drop
 000077: 41 00                      | i32.const 0
 000079: 1a                         | drop
 00007a: 41 00                      | i32.const 0
 00007c: 1a                         | drop
 00007d: 41 00                      | i32.const 0
 00007f: 1a                         | drop
 000080: 41 00                      | i32.const 0
 000082: 1a                         | drop
 000083: 41 00                      | i32.const 0
 000085: 1a                         | drop
 000086: 41 00                      | i32.const 0
 000088: 1a                         | drop
 000089: 41 00                      | i32.const 0
 00008b: 1a                         | drop
 00008c: 41 00                      | i32.const 0
 00008e: 1a                         | drop
 00008f: 41 00                      | i32.const 0
 000091: 1a                         | drop
 000092: 41 00                      | i32.const 0
 000094: 1a                         | drop
 000095: 41 00                      | i32.const 0
 000097: 1a                         | drop
 000098: 41 00                      | i32.const 0
 00009a: 1a                         | drop
 00009b: 41 00                      | i32.const 0
 00009d: 1a                         | drop
 00009e: 41 00                      | i32.const 0
 0000a0: 1a                         | drop
 0000a1: 41 00                      | i32.const 0
 0000a3: 1a                         | drop
 0000a4: 41 00                      | i32.const 0
 0000a6: 1a                         | drop
 0000a7: 41 00                      | i32.const 0
 0000a9: 1a                         | drop
 0000aa: 41 00                      | i32.const 0
 0000ac: 1a                         | drop
 0000ad: 41 00                      | i32.const 0
 0000af: 1a                         | drop
 0000b0: 0b                         | end
;;; STDOUT ;;)
