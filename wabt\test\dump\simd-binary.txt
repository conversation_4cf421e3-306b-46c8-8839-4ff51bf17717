;;; TOOL: run-objdump

(module
  ;; i8x16 add
  (func (export "i8x16_add_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i8x16.add)

  ;; i16x8 add
  (func (export "i16x8_add_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.add)

  ;; i32x4 add
  (func (export "i32x4_add_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i32x4.add)

  ;; i64x2 add
  (func (export "i64x2_add_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i64x2.add)

  ;; i8x16 sub
  (func (export "i8x16_sub_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i8x16.sub)

  ;; i16x8 sub
  (func (export "i16x8_sub_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.sub)

  ;; i32x4 sub
  (func (export "i32x4_sub_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i32x4.sub)

  ;; i64x2 sub
  (func (export "i64x2_sub_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i64x2.sub)

  ;; i16x8 mul
  (func (export "i16x8_mul_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.mul)

  ;; i32x4 mul
  (func (export "i32x4_mul_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i32x4.mul)

  ;; i64x2 mul
  (func (export "i64x2_mul_0") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i64x2.mul)

  ;; i8x16 saturating add (signed and unsigned)
  (func (export "i8x16_add_saturate_signed_0") (result v128)
    v128.const i32x4 0x00000001 0x0000007f 0x00000003 0x00000080
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x000000ff
    i8x16.add_sat_s)
  (func (export "i8x16_add_saturate_unsigned_0") (result v128)
    v128.const i32x4 0x00ff0001 0x04000002 0x00000003 0x00000004
    v128.const i32x4 0x00020001 0xfe000002 0x00000003 0x00000004
    i8x16.add_sat_u)

  ;; i16x8 saturating add (signed and unsigned)
  (func (export "i16x8_add_saturate_signed_0") (result v128)
    v128.const i32x4 0x00000001 0x00007fff 0x00000003 0x00008000
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x0000fffe
    i16x8.add_sat_s)
  (func (export "i16x8_add_saturate_unsigned_0") (result v128)
    v128.const i32x4 0x00ffffff 0x0400ffff 0x00000003 0x00000004
    v128.const i32x4 0x00020001 0xfe000002 0x00000003 0x00000004
    i16x8.add_sat_u)

  ;; i8x16 saturating sub (signed and unsigned)
  (func (export "i8x16_sub_saturate_signed_0") (result v128)
    v128.const i32x4 0x00000001 0x0000007f 0x000000fe 0x00000080
    v128.const i32x4 0x00000001 0x000000fe 0x0000007f 0x000000ff
    i8x16.sub_sat_s)
  (func (export "i8x16_sub_saturate_unsigned_0") (result v128)
    v128.const i32x4 0x00ff0001 0x0400007f 0x0000fffe 0x00000004
    v128.const i32x4 0x00020001 0xfe00fffe 0x0000007f 0x00000004
    i8x16.sub_sat_u)

  ;; i16x8 saturating sub (signed and unsigned)
  (func (export "i16x8_sub_saturate_signed_0") (result v128)
    v128.const i32x4 0x00000001 0x00007fff 0x0000fffe 0x00008000
    v128.const i32x4 0x00000001 0x0000fffe 0x00007fff 0x0000fffe
    i16x8.sub_sat_s)
  (func (export "i16x8_sub_saturate_unsigned_0") (result v128)
    v128.const i32x4 0x00ffffff 0x0400ffff 0x00000003 0x00000004
    v128.const i32x4 0x00020001 0xfe000002 0x00000003 0x00000004
    i16x8.sub_sat_u)

  ;; v128 and
  (func (export "v128_and_0") (result v128)
    v128.const i32x4 0x00ff0001 0x00040002 0x44000003 0x00000004
    v128.const i32x4 0x00020001 0x00fe0002 0x00000003 0x55000004
    v128.and)

  ;; v128 or
  (func (export "v128_or_0") (result v128)
    v128.const i32x4 0x00ff0001 0x00040002 0x44000003 0x00000004
    v128.const i32x4 0x00020001 0x00fe0002 0x00000003 0x55000004
    v128.or)

  ;; v128 xor
  (func (export "v128_xor_0") (result v128)
    v128.const i32x4 0x00ff0001 0x00040002 0x44000003 0x00000004
    v128.const i32x4 0x00020001 0x00fe0002 0x00000003 0x55000004
    v128.xor)

  ;; f32x4 min
  (func (export "f32x4_min_0") (result v128)
    v128.const i32x4 0x80000000 0xffc00000 0x449a5000 0xbf800000
    v128.const i32x4 0x00000000 0x3f800000 0x449a5000 0x3f800000
    f32x4.min)

  ;; f64x2 min
  (func (export "f64x2_min_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0xc0934a00 0x00000000 0x3ff00000
    f64x2.min)

  ;; f32x4 max
  (func (export "f32x4_max_0") (result v128)
    v128.const i32x4 0x80000000 0xffc00000 0x449a5000 0xbf800000
    v128.const i32x4 0x00000000 0x3f800000 0x449a5000 0x3f800000
    f32x4.max)

  ;; f64x2 max
  (func (export "f64x2_max_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0xc0934a00 0x00000000 0x3ff00000
    f64x2.max)

  ;; f32x4 add
  (func (export "f32x4_add_0") (result v128)
    v128.const i32x4 0x80000000 0xffc00000 0x449a5000 0xc49a5000
    v128.const i32x4 0x00000000 0x3f800000 0x3f800000 0x3fc00000
    f32x4.add)

  ;; f64x2 add
  (func (export "f64x2_add_0") (result v128)
    v128.const i32x4 0x00000000 0x3ff80000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0xc0934a00 0x00000000 0x3ff00000
    f64x2.add)

  ;; f32x4 sub
  (func (export "f32x4_sub_0") (result v128)
    v128.const i32x4 0x80000000 0xffc00000 0x449a5000 0xc49a5000
    v128.const i32x4 0x00000000 0x3f800000 0x3f800000 0x3fc00000
    f32x4.sub)

  ;; f64x2 sub
  (func (export "f64x2_sub_0") (result v128)
    v128.const i32x4 0x00000000 0x3ff80000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0xc0934a00 0x00000000 0x3ff00000
    f64x2.sub)

  ;; f32x4 div
  (func (export "f32x4_div_0") (result v128)
    v128.const i32x4 0x80000000 0xffc00000 0x3fc00000 0xc0400000
    v128.const i32x4 0x00000000 0x3f800000 0x3f800000 0x3fc00000
    f32x4.div)

  ;; f64x2 div
  (func (export "f64x2_div_0") (result v128)
    v128.const i32x4 0x00000000 0x3ff80000 0x00000000 0xc0080000
    v128.const i32x4 0x00000000 0x3ff00000 0x00000000 0x3ff80000
    f64x2.div)

  ;; f32x4 mul
  (func (export "f32x4_mul_0") (result v128)
    v128.const i32x4 0x80000000 0xffc00000 0x3fc00000 0xc0400000
    v128.const i32x4 0x00000000 0x3f800000 0x3f800000 0x3fc00000
    f32x4.mul)

  ;; f64x2 mul
  (func (export "f64x2_mul_0") (result v128)
    v128.const i32x4 0x00000000 0x3ff80000 0x00000000 0xc0080000
    v128.const i32x4 0x00000000 0x3ff00000 0x00000000 0x3ff80000
    f64x2.mul)

  ;; i8x16.narrow_i16x8_s
  (func (export "i8x16.narrow_i16x8_s") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i8x16.narrow_i16x8_s)

  ;; i8x16.narrow_i16x8_u
  (func (export "i8x16.narrow_i16x8_u") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i8x16.narrow_i16x8_u)

  ;; i16x8.narrow_i32x4_s
  (func (export "i16x8.narrow_i32x4_s") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.narrow_i32x4_s)

  ;; i16x8.narrow_i32x4_u
  (func (export "i16x8.narrow_i32x4_u") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.narrow_i32x4_u)

  ;; i16x8.extend_low_i8x16_s
  (func (export "i16x8.extend_low_i8x16_s") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.extend_low_i8x16_s)

  ;; i16x8.extend_high_i8x16_s
  (func (export "i16x8.extend_high_i8x16_s") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.extend_high_i8x16_s)

  ;; i16x8.extend_low_i8x16_u
  (func (export "i16x8.extend_low_i8x16_u") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.extend_low_i8x16_u)

  ;; i16x8.extend_high_i8x16_u
  (func (export "i16x8.extend_high_i8x16_u") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.extend_high_i8x16_u)

  ;; i32x4.extend_low_i16x8_s
  (func (export "i32x4.extend_low_i16x8_s") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i32x4.extend_low_i16x8_s)

  ;; i32x4.extend_high_i16x8_s
  (func (export "i32x4.extend_high_i16x8_s") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i32x4.extend_high_i16x8_s)

  ;; i32x4.extend_low_i16x8_u
  (func (export "i32x4.extend_low_i16x8_u") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i32x4.extend_low_i16x8_u)

  ;; i32x4.extend_high_i16x8_u
  (func (export "i32x4.extend_high_i16x8_u") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i32x4.extend_high_i16x8_u)

  ;; v128.andnot
  (func (export "v128.andnot") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.andnot)

  ;; i8x16.avgr_u
  (func (export "i8x16.avgr_u") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i8x16.avgr_u)

  ;; i16x8.avgr_u
  (func (export "i16x8.avgr_u") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    i16x8.avgr_u)
)

(;; STDOUT ;;;

simd-binary.wasm:	file format wasm 0x1

Code Disassembly:

000410 func[0] <i8x16_add_0>:
 000411: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 00041a: 00 03 00 00 00 04 00 00 00 | 
 000423: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 00042c: 00 03 00 00 00 04 00 00 00 | 
 000435: fd 6e                      | i8x16.add
 000437: 0b                         | end
000439 func[1] <i16x8_add_0>:
 00043a: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000443: 00 03 00 00 00 04 00 00 00 | 
 00044c: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000455: 00 03 00 00 00 04 00 00 00 | 
 00045e: fd 8e 01                   | i16x8.add
 000461: 0b                         | end
000463 func[2] <i32x4_add_0>:
 000464: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 00046d: 00 03 00 00 00 04 00 00 00 | 
 000476: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 00047f: 00 03 00 00 00 04 00 00 00 | 
 000488: fd ae 01                   | i32x4.add
 00048b: 0b                         | end
00048d func[3] <i64x2_add_0>:
 00048e: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000497: 00 03 00 00 00 04 00 00 00 | 
 0004a0: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0004a9: 00 03 00 00 00 04 00 00 00 | 
 0004b2: fd ce 01                   | i64x2.add
 0004b5: 0b                         | end
0004b7 func[4] <i8x16_sub_0>:
 0004b8: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0004c1: 00 03 00 00 00 04 00 00 00 | 
 0004ca: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0004d3: 00 03 00 00 00 04 00 00 00 | 
 0004dc: fd 71                      | i8x16.sub
 0004de: 0b                         | end
0004e0 func[5] <i16x8_sub_0>:
 0004e1: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0004ea: 00 03 00 00 00 04 00 00 00 | 
 0004f3: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0004fc: 00 03 00 00 00 04 00 00 00 | 
 000505: fd 91 01                   | i16x8.sub
 000508: 0b                         | end
00050a func[6] <i32x4_sub_0>:
 00050b: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000514: 00 03 00 00 00 04 00 00 00 | 
 00051d: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000526: 00 03 00 00 00 04 00 00 00 | 
 00052f: fd b1 01                   | i32x4.sub
 000532: 0b                         | end
000534 func[7] <i64x2_sub_0>:
 000535: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 00053e: 00 03 00 00 00 04 00 00 00 | 
 000547: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000550: 00 03 00 00 00 04 00 00 00 | 
 000559: fd d1 01                   | i64x2.sub
 00055c: 0b                         | end
00055e func[8] <i16x8_mul_0>:
 00055f: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000568: 00 03 00 00 00 04 00 00 00 | 
 000571: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 00057a: 00 03 00 00 00 04 00 00 00 | 
 000583: fd 95 01                   | i16x8.mul
 000586: 0b                         | end
000588 func[9] <i32x4_mul_0>:
 000589: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000592: 00 03 00 00 00 04 00 00 00 | 
 00059b: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0005a4: 00 03 00 00 00 04 00 00 00 | 
 0005ad: fd b5 01                   | i32x4.mul
 0005b0: 0b                         | end
0005b2 func[10] <i64x2_mul_0>:
 0005b3: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0005bc: 00 03 00 00 00 04 00 00 00 | 
 0005c5: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0005ce: 00 03 00 00 00 04 00 00 00 | 
 0005d7: fd d5 01                   | i64x2.mul
 0005da: 0b                         | end
0005dc func[11] <i8x16_add_saturate_signed_0>:
 0005dd: fd 0c 01 00 00 00 7f 00 00 | v128.const 0x00000001 0x0000007f 0x00000003 0x00000080
 0005e6: 00 03 00 00 00 80 00 00 00 | 
 0005ef: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x000000ff
 0005f8: 00 03 00 00 00 ff 00 00 00 | 
 000601: fd 6f                      | i8x16.add_sat_s
 000603: 0b                         | end
000605 func[12] <i8x16_add_saturate_unsigned_0>:
 000606: fd 0c 01 00 ff 00 02 00 00 | v128.const 0x00ff0001 0x04000002 0x00000003 0x00000004
 00060f: 04 03 00 00 00 04 00 00 00 | 
 000618: fd 0c 01 00 02 00 02 00 00 | v128.const 0x00020001 0xfe000002 0x00000003 0x00000004
 000621: fe 03 00 00 00 04 00 00 00 | 
 00062a: fd 70                      | i8x16.add_sat_u
 00062c: 0b                         | end
00062e func[13] <i16x8_add_saturate_signed_0>:
 00062f: fd 0c 01 00 00 00 ff 7f 00 | v128.const 0x00000001 0x00007fff 0x00000003 0x00008000
 000638: 00 03 00 00 00 00 80 00 00 | 
 000641: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x0000fffe
 00064a: 00 03 00 00 00 fe ff 00 00 | 
 000653: fd 8f 01                   | i16x8.add_sat_s
 000656: 0b                         | end
000658 func[14] <i16x8_add_saturate_unsigned_0>:
 000659: fd 0c ff ff ff 00 ff ff 00 | v128.const 0x00ffffff 0x0400ffff 0x00000003 0x00000004
 000662: 04 03 00 00 00 04 00 00 00 | 
 00066b: fd 0c 01 00 02 00 02 00 00 | v128.const 0x00020001 0xfe000002 0x00000003 0x00000004
 000674: fe 03 00 00 00 04 00 00 00 | 
 00067d: fd 90 01                   | i16x8.add_sat_u
 000680: 0b                         | end
000682 func[15] <i8x16_sub_saturate_signed_0>:
 000683: fd 0c 01 00 00 00 7f 00 00 | v128.const 0x00000001 0x0000007f 0x000000fe 0x00000080
 00068c: 00 fe 00 00 00 80 00 00 00 | 
 000695: fd 0c 01 00 00 00 fe 00 00 | v128.const 0x00000001 0x000000fe 0x0000007f 0x000000ff
 00069e: 00 7f 00 00 00 ff 00 00 00 | 
 0006a7: fd 72                      | i8x16.sub_sat_s
 0006a9: 0b                         | end
0006ab func[16] <i8x16_sub_saturate_unsigned_0>:
 0006ac: fd 0c 01 00 ff 00 7f 00 00 | v128.const 0x00ff0001 0x0400007f 0x0000fffe 0x00000004
 0006b5: 04 fe ff 00 00 04 00 00 00 | 
 0006be: fd 0c 01 00 02 00 fe ff 00 | v128.const 0x00020001 0xfe00fffe 0x0000007f 0x00000004
 0006c7: fe 7f 00 00 00 04 00 00 00 | 
 0006d0: fd 73                      | i8x16.sub_sat_u
 0006d2: 0b                         | end
0006d4 func[17] <i16x8_sub_saturate_signed_0>:
 0006d5: fd 0c 01 00 00 00 ff 7f 00 | v128.const 0x00000001 0x00007fff 0x0000fffe 0x00008000
 0006de: 00 fe ff 00 00 00 80 00 00 | 
 0006e7: fd 0c 01 00 00 00 fe ff 00 | v128.const 0x00000001 0x0000fffe 0x00007fff 0x0000fffe
 0006f0: 00 ff 7f 00 00 fe ff 00 00 | 
 0006f9: fd 92 01                   | i16x8.sub_sat_s
 0006fc: 0b                         | end
0006fe func[18] <i16x8_sub_saturate_unsigned_0>:
 0006ff: fd 0c ff ff ff 00 ff ff 00 | v128.const 0x00ffffff 0x0400ffff 0x00000003 0x00000004
 000708: 04 03 00 00 00 04 00 00 00 | 
 000711: fd 0c 01 00 02 00 02 00 00 | v128.const 0x00020001 0xfe000002 0x00000003 0x00000004
 00071a: fe 03 00 00 00 04 00 00 00 | 
 000723: fd 93 01                   | i16x8.sub_sat_u
 000726: 0b                         | end
000728 func[19] <v128_and_0>:
 000729: fd 0c 01 00 ff 00 02 00 04 | v128.const 0x00ff0001 0x00040002 0x44000003 0x00000004
 000732: 00 03 00 00 44 04 00 00 00 | 
 00073b: fd 0c 01 00 02 00 02 00 fe | v128.const 0x00020001 0x00fe0002 0x00000003 0x55000004
 000744: 00 03 00 00 00 04 00 00 55 | 
 00074d: fd 4e                      | v128.and
 00074f: 0b                         | end
000751 func[20] <v128_or_0>:
 000752: fd 0c 01 00 ff 00 02 00 04 | v128.const 0x00ff0001 0x00040002 0x44000003 0x00000004
 00075b: 00 03 00 00 44 04 00 00 00 | 
 000764: fd 0c 01 00 02 00 02 00 fe | v128.const 0x00020001 0x00fe0002 0x00000003 0x55000004
 00076d: 00 03 00 00 00 04 00 00 55 | 
 000776: fd 50                      | v128.or
 000778: 0b                         | end
00077a func[21] <v128_xor_0>:
 00077b: fd 0c 01 00 ff 00 02 00 04 | v128.const 0x00ff0001 0x00040002 0x44000003 0x00000004
 000784: 00 03 00 00 44 04 00 00 00 | 
 00078d: fd 0c 01 00 02 00 02 00 fe | v128.const 0x00020001 0x00fe0002 0x00000003 0x55000004
 000796: 00 03 00 00 00 04 00 00 55 | 
 00079f: fd 51                      | v128.xor
 0007a1: 0b                         | end
0007a3 func[22] <f32x4_min_0>:
 0007a4: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x449a5000 0xbf800000
 0007ad: ff 00 50 9a 44 00 00 80 bf | 
 0007b6: fd 0c 00 00 00 00 00 00 80 | v128.const 0x00000000 0x3f800000 0x449a5000 0x3f800000
 0007bf: 3f 00 50 9a 44 00 00 80 3f | 
 0007c8: fd e8 01                   | f32x4.min
 0007cb: 0b                         | end
0007cd func[23] <f64x2_min_0>:
 0007ce: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 0007d7: 00 00 00 00 00 00 00 f8 ff | 
 0007e0: fd 0c 00 00 00 00 00 4a 93 | v128.const 0x00000000 0xc0934a00 0x00000000 0x3ff00000
 0007e9: c0 00 00 00 00 00 00 f0 3f | 
 0007f2: fd f4 01                   | f64x2.min
 0007f5: 0b                         | end
0007f7 func[24] <f32x4_max_0>:
 0007f8: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x449a5000 0xbf800000
 000801: ff 00 50 9a 44 00 00 80 bf | 
 00080a: fd 0c 00 00 00 00 00 00 80 | v128.const 0x00000000 0x3f800000 0x449a5000 0x3f800000
 000813: 3f 00 50 9a 44 00 00 80 3f | 
 00081c: fd e9 01                   | f32x4.max
 00081f: 0b                         | end
000821 func[25] <f64x2_max_0>:
 000822: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 00082b: 00 00 00 00 00 00 00 f8 ff | 
 000834: fd 0c 00 00 00 00 00 4a 93 | v128.const 0x00000000 0xc0934a00 0x00000000 0x3ff00000
 00083d: c0 00 00 00 00 00 00 f0 3f | 
 000846: fd f5 01                   | f64x2.max
 000849: 0b                         | end
00084b func[26] <f32x4_add_0>:
 00084c: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x449a5000 0xc49a5000
 000855: ff 00 50 9a 44 00 50 9a c4 | 
 00085e: fd 0c 00 00 00 00 00 00 80 | v128.const 0x00000000 0x3f800000 0x3f800000 0x3fc00000
 000867: 3f 00 00 80 3f 00 00 c0 3f | 
 000870: fd e4 01                   | f32x4.add
 000873: 0b                         | end
000875 func[27] <f64x2_add_0>:
 000876: fd 0c 00 00 00 00 00 00 f8 | v128.const 0x00000000 0x3ff80000 0x00000000 0xfff80000
 00087f: 3f 00 00 00 00 00 00 f8 ff | 
 000888: fd 0c 00 00 00 00 00 4a 93 | v128.const 0x00000000 0xc0934a00 0x00000000 0x3ff00000
 000891: c0 00 00 00 00 00 00 f0 3f | 
 00089a: fd f0 01                   | f64x2.add
 00089d: 0b                         | end
00089f func[28] <f32x4_sub_0>:
 0008a0: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x449a5000 0xc49a5000
 0008a9: ff 00 50 9a 44 00 50 9a c4 | 
 0008b2: fd 0c 00 00 00 00 00 00 80 | v128.const 0x00000000 0x3f800000 0x3f800000 0x3fc00000
 0008bb: 3f 00 00 80 3f 00 00 c0 3f | 
 0008c4: fd e5 01                   | f32x4.sub
 0008c7: 0b                         | end
0008c9 func[29] <f64x2_sub_0>:
 0008ca: fd 0c 00 00 00 00 00 00 f8 | v128.const 0x00000000 0x3ff80000 0x00000000 0xfff80000
 0008d3: 3f 00 00 00 00 00 00 f8 ff | 
 0008dc: fd 0c 00 00 00 00 00 4a 93 | v128.const 0x00000000 0xc0934a00 0x00000000 0x3ff00000
 0008e5: c0 00 00 00 00 00 00 f0 3f | 
 0008ee: fd f1 01                   | f64x2.sub
 0008f1: 0b                         | end
0008f3 func[30] <f32x4_div_0>:
 0008f4: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x3fc00000 0xc0400000
 0008fd: ff 00 00 c0 3f 00 00 40 c0 | 
 000906: fd 0c 00 00 00 00 00 00 80 | v128.const 0x00000000 0x3f800000 0x3f800000 0x3fc00000
 00090f: 3f 00 00 80 3f 00 00 c0 3f | 
 000918: fd e7 01                   | f32x4.div
 00091b: 0b                         | end
00091d func[31] <f64x2_div_0>:
 00091e: fd 0c 00 00 00 00 00 00 f8 | v128.const 0x00000000 0x3ff80000 0x00000000 0xc0080000
 000927: 3f 00 00 00 00 00 00 08 c0 | 
 000930: fd 0c 00 00 00 00 00 00 f0 | v128.const 0x00000000 0x3ff00000 0x00000000 0x3ff80000
 000939: 3f 00 00 00 00 00 00 f8 3f | 
 000942: fd f3 01                   | f64x2.div
 000945: 0b                         | end
000947 func[32] <f32x4_mul_0>:
 000948: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x3fc00000 0xc0400000
 000951: ff 00 00 c0 3f 00 00 40 c0 | 
 00095a: fd 0c 00 00 00 00 00 00 80 | v128.const 0x00000000 0x3f800000 0x3f800000 0x3fc00000
 000963: 3f 00 00 80 3f 00 00 c0 3f | 
 00096c: fd e6 01                   | f32x4.mul
 00096f: 0b                         | end
000971 func[33] <f64x2_mul_0>:
 000972: fd 0c 00 00 00 00 00 00 f8 | v128.const 0x00000000 0x3ff80000 0x00000000 0xc0080000
 00097b: 3f 00 00 00 00 00 00 08 c0 | 
 000984: fd 0c 00 00 00 00 00 00 f0 | v128.const 0x00000000 0x3ff00000 0x00000000 0x3ff80000
 00098d: 3f 00 00 00 00 00 00 f8 3f | 
 000996: fd f2 01                   | f64x2.mul
 000999: 0b                         | end
00099b func[34] <i8x16.narrow_i16x8_s>:
 00099c: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0009a5: 00 03 00 00 00 04 00 00 00 | 
 0009ae: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0009b7: 00 03 00 00 00 04 00 00 00 | 
 0009c0: fd 65                      | i8x16.narrow_i16x8_s
 0009c2: 0b                         | end
0009c4 func[35] <i8x16.narrow_i16x8_u>:
 0009c5: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0009ce: 00 03 00 00 00 04 00 00 00 | 
 0009d7: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0009e0: 00 03 00 00 00 04 00 00 00 | 
 0009e9: fd 66                      | i8x16.narrow_i16x8_u
 0009eb: 0b                         | end
0009ed func[36] <i16x8.narrow_i32x4_s>:
 0009ee: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 0009f7: 00 03 00 00 00 04 00 00 00 | 
 000a00: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000a09: 00 03 00 00 00 04 00 00 00 | 
 000a12: fd 85 01                   | i16x8.narrow_i32x4_s
 000a15: 0b                         | end
000a17 func[37] <i16x8.narrow_i32x4_u>:
 000a18: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000a21: 00 03 00 00 00 04 00 00 00 | 
 000a2a: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000a33: 00 03 00 00 00 04 00 00 00 | 
 000a3c: fd 86 01                   | i16x8.narrow_i32x4_u
 000a3f: 0b                         | end
000a41 func[38] <i16x8.extend_low_i8x16_s>:
 000a42: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000a4b: 00 03 00 00 00 04 00 00 00 | 
 000a54: fd 87 01                   | i16x8.extend_low_i8x16_s
 000a57: 0b                         | end
000a59 func[39] <i16x8.extend_high_i8x16_s>:
 000a5a: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000a63: 00 03 00 00 00 04 00 00 00 | 
 000a6c: fd 88 01                   | i16x8.extend_high_i8x16_s
 000a6f: 0b                         | end
000a71 func[40] <i16x8.extend_low_i8x16_u>:
 000a72: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000a7b: 00 03 00 00 00 04 00 00 00 | 
 000a84: fd 89 01                   | i16x8.extend_low_i8x16_u
 000a87: 0b                         | end
000a89 func[41] <i16x8.extend_high_i8x16_u>:
 000a8a: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000a93: 00 03 00 00 00 04 00 00 00 | 
 000a9c: fd 8a 01                   | i16x8.extend_high_i8x16_u
 000a9f: 0b                         | end
000aa1 func[42] <i32x4.extend_low_i16x8_s>:
 000aa2: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000aab: 00 03 00 00 00 04 00 00 00 | 
 000ab4: fd a7 01                   | i32x4.extend_low_i16x8_s
 000ab7: 0b                         | end
000ab9 func[43] <i32x4.extend_high_i16x8_s>:
 000aba: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000ac3: 00 03 00 00 00 04 00 00 00 | 
 000acc: fd a8 01                   | i32x4.extend_high_i16x8_s
 000acf: 0b                         | end
000ad1 func[44] <i32x4.extend_low_i16x8_u>:
 000ad2: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000adb: 00 03 00 00 00 04 00 00 00 | 
 000ae4: fd a9 01                   | i32x4.extend_low_i16x8_u
 000ae7: 0b                         | end
000ae9 func[45] <i32x4.extend_high_i16x8_u>:
 000aea: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000af3: 00 03 00 00 00 04 00 00 00 | 
 000afc: fd aa 01                   | i32x4.extend_high_i16x8_u
 000aff: 0b                         | end
000b01 func[46] <v128.andnot>:
 000b02: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000b0b: 00 03 00 00 00 04 00 00 00 | 
 000b14: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000b1d: 00 03 00 00 00 04 00 00 00 | 
 000b26: fd 4f                      | v128.andnot
 000b28: 0b                         | end
000b2a func[47] <i8x16.avgr_u>:
 000b2b: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000b34: 00 03 00 00 00 04 00 00 00 | 
 000b3d: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000b46: 00 03 00 00 00 04 00 00 00 | 
 000b4f: fd 7b                      | i8x16.avgr_u
 000b51: 0b                         | end
000b53 func[48] <i16x8.avgr_u>:
 000b54: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000b5d: 00 03 00 00 00 04 00 00 00 | 
 000b66: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 000b6f: 00 03 00 00 00 04 00 00 00 | 
 000b78: fd 9b 01                   | i16x8.avgr_u
 000b7b: 0b                         | end
;;; STDOUT ;;)
