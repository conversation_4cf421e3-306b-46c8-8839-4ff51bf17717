;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[0]}
section(FUNCTION) { count[1] type[0] }
section(MEMORY) { count[1] flags[0] init[0] }
section(CODE) {
  count[1]
  func {
    locals[0]
    i32.const 0
    ;; The memory.grow reserved byte must be a single 0 byte. Using a long
    ;; leb128 encoding of 0 is not valid.
    memory.grow reserved[0x80 0]
    drop
  }
}
(;; STDERR ;;;
0000020: error: memory.grow reserved value must be 0
0000020: error: memory.grow reserved value must be 0
;;; STDERR ;;)
