;;; TOOL: run-objdump
;;; ARGS0: -v --no-check
(module
  (func (result i64)
    f32.const 1
    f64.const 2
    i32.add)
  (export "foo" (func 0)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 01                                        ; num results
000000e: 7e                                        ; i64
0000009: 05                                        ; FIXUP section size
; section "Function" (3)
000000f: 03                                        ; section code
0000010: 00                                        ; section size (guess)
0000011: 01                                        ; num functions
0000012: 00                                        ; function 0 signature index
0000010: 02                                        ; FIXUP section size
; section "Export" (7)
0000013: 07                                        ; section code
0000014: 00                                        ; section size (guess)
0000015: 01                                        ; num exports
0000016: 03                                        ; string length
0000017: 666f 6f                                  foo  ; export name
000001a: 00                                        ; export kind
000001b: 00                                        ; export func index
0000014: 07                                        ; FIXUP section size
; section "Code" (10)
000001c: 0a                                        ; section code
000001d: 00                                        ; section size (guess)
000001e: 01                                        ; num functions
; function body 0
000001f: 00                                        ; func body size (guess)
0000020: 00                                        ; local decl count
0000021: 43                                        ; f32.const
0000022: 0000 803f                                 ; f32 literal
0000026: 44                                        ; f64.const
0000027: 0000 0000 0000 0040                       ; f64 literal
000002f: 6a                                        ; i32.add
0000030: 0b                                        ; end
000001f: 11                                        ; FIXUP func body size
000001d: 13                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

nocheck.wasm:	file format wasm 0x1

Code Disassembly:

000020 func[0] <foo>:
 000021: 43 00 00 80 3f             | f32.const 0x1p+0
 000026: 44 00 00 00 00 00 00 00 40 | f64.const 0x1p+1
 00002f: 6a                         | i32.add
 000030: 0b                         | end
;;; STDOUT ;;)
