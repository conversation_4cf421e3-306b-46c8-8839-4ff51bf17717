# AFL dictionary for the WAST format

open="("
close=")"
comment=";;"
block_comment_open="(;"
block_comment_close=";)"

param_i32="(param i32)"
param_i64="(param i64)"
param_f32="(param f32)"
param_f64="(param f64)"

result_i32="(result i32)"
result_i64="(result i64)"
result_f32="(result f32)"
result_f64="(result f64)"

type_i32="i32"
type_i64="i64"
type_f32="f32"
type_f64="f64"

misc_name="$foobar"
misc_int="42"
misc_int_hex="0xdeadcode"
misc_float="3.14159"
misc_float_exp="1e100"
misc_float_hex="0xcab.ba6ep4"
misc_float_inf="infinity"
misc_float_nan="nan:0xf00baa"
misc_float_sign="-6.02e-23"

op_nop="nop"
op_block="block"
op_if="if"
op_then="then"
op_else="else"
op_loop="loop"
op_br="br"
op_br_if="br_if"
op_br_table="br_table"
op_call="call"
op_call_indirect="call_indirect"
op_drop="drop"
op_end="end"
op_return="return"
op_local_get="local.get"
op_local_set="local.set"
op_local_tee="local.tee"
op_global_get="global.get"
op_global_set="global.set"
op_i32_load="i32.load"
op_i64_load="i64.load"
op_f32_load="f32.load"
op_f64_load="f64.load"
op_i32_store="i32.store"
op_i64_store="i64.store"
op_f32_store="f32.store"
op_f64_store="f64.store"
op_i32_load8_s="i32.load8_s"
op_i64_load8_s="i64.load8_s"
op_i32_load8_u="i32.load8_u"
op_i64_load8_u="i64.load8_u"
op_i32_load16_s="i32.load16_s"
op_i64_load16_s="i64.load16_s"
op_i32_load16_u="i32.load16_u"
op_i64_load16_u="i64.load16_u"
op_i64_load32_s="i64.load32_s"
op_i64_load32_u="i64.load32_u"
op_i32_store8="i32.store8"
op_i64_store8="i64.store8"
op_i32_store16="i32.store16"
op_i64_store16="i64.store16"
op_i64_store32="i64.store32"
op_i32_const="i32.const"
op_i64_const="i64.const"
op_f32_const="f32.const"
op_f64_const="f64.const"
op_i32_eqz="i32.eqz"
op_i64_eqz="i64.eqz"
op_i32_clz="i32.clz"
op_i64_clz="i64.clz"
op_i32_ctz="i32.ctz"
op_i64_ctz="i64.ctz"
op_i32_popcnt="i32.popcnt"
op_i64_popcnt="i64.popcnt"
op_f32_neg="f32.neg"
op_f64_neg="f64.neg"
op_f32_abs="f32.abs"
op_f64_abs="f64.abs"
op_f32_sqrt="f32.sqrt"
op_f64_sqrt="f64.sqrt"
op_f32_ceil="f32.ceil"
op_f64_ceil="f64.ceil"
op_f32_floor="f32.floor"
op_f64_floor="f64.floor"
op_f32_trunc="f32.trunc"
op_f64_trunc="f64.trunc"
op_f32_nearest="f32.nearest"
op_f64_nearest="f64.nearest"
op_i32_add="i32.add"
op_i64_add="i64.add"
op_i32_sub="i32.sub"
op_i64_sub="i64.sub"
op_i32_mul="i32.mul"
op_i64_mul="i64.mul"
op_i32_div_s="i32.div_s"
op_i64_div_s="i64.div_s"
op_i32_div_u="i32.div_u"
op_i64_div_u="i64.div_u"
op_i32_rem_s="i32.rem_s"
op_i64_rem_s="i64.rem_s"
op_i32_rem_u="i32.rem_u"
op_i64_rem_u="i64.rem_u"
op_i32_and="i32.and"
op_i64_and="i64.and"
op_i32_or="i32.or"
op_i64_or="i64.or"
op_i32_xor="i32.xor"
op_i64_xor="i64.xor"
op_i32_shl="i32.shl"
op_i64_shl="i64.shl"
op_i32_shr_s="i32.shr_s"
op_i64_shr_s="i64.shr_s"
op_i32_shr_u="i32.shr_u"
op_i64_shr_u="i64.shr_u"
op_i32_rotl="i32.rotl"
op_i64_rotl="i64.rotl"
op_f32_add="f32.add"
op_f64_add="f64.add"
op_f32_sub="f32.sub"
op_f64_sub="f64.sub"
op_f32_mul="f32.mul"
op_f64_mul="f64.mul"
op_f32_div="f32.div"
op_f64_div="f64.div"
op_f32_min="f32.min"
op_f64_min="f64.min"
op_f32_max="f32.max"
op_f64_max="f64.max"
op_f32_copysign="f32.copysign"
op_f64_copysign="f64.copysign"
op_i32_eq="i32.eq"
op_i64_eq="i64.eq"
op_i32_ne="i32.ne"
op_i64_ne="i64.ne"
op_i32_lt_s="i32.lt_s"
op_i64_lt_s="i64.lt_s"
op_i32_lt_u="i32.lt_u"
op_i64_lt_u="i64.lt_u"
op_i32_le_s="i32.le_s"
op_i64_le_s="i64.le_s"
op_i32_le_u="i32.le_u"
op_i64_le_u="i64.le_u"
op_i32_gt_s="i32.gt_s"
op_i64_gt_s="i64.gt_s"
op_i32_gt_u="i32.gt_u"
op_i64_gt_u="i64.gt_u"
op_i32_ge_s="i32.ge_s"
op_i64_ge_s="i64.ge_s"
op_i32_ge_u="i32.ge_u"
op_i64_ge_u="i64.ge_u"
op_f32_eq="f32.eq"
op_f64_eq="f64.eq"
op_f32_ne="f32.ne"
op_f64_ne="f64.ne"
op_f32_lt="f32.lt"
op_f64_lt="f64.lt"
op_f32_le="f32.le"
op_f64_le="f64.le"
op_f32_gt="f32.gt"
op_f64_gt="f64.gt"
op_f32_ge="f32.ge"
op_f64_ge="f64.ge"
op_i64_extend_i32_s="i64.extend_i32_s"
op_i64_extend_i32_u="i64.extend_i32_u"
op_i32_wrap_i64="i32.wrap_i64"
op_i32_trunc_f32_s="i32.trunc_f32_s"
op_i64_trunc_f32_s="i64.trunc_f32_s"
op_i32_trunc_f64_s="i32.trunc_f64_s"
op_i64_trunc_f64_s="i64.trunc_f64_s"
op_i32_trunc_f32_u="i32.trunc_f32_u"
op_i64_trunc_f32_u="i64.trunc_f32_u"
op_i32_trunc_f64_u="i32.trunc_f64_u"
op_i64_trunc_f64_u="i64.trunc_f64_u"
op_f32_convert_i32_s="f32.convert_i32_s"
op_f64_convert_i32_s="f64.convert_i32_s"
op_f32_convert_i64_s="f32.convert_i64_s"
op_f64_convert_i64_s="f64.convert_i64_s"
op_f32_convert_i32_u="f32.convert_i32_u"
op_f64_convert_i32_u="f64.convert_i32_u"
op_f32_convert_i64_u="f32.convert_i64_u"
op_f64_convert_i64_u="f64.convert_i64_u"
op_f64_promote_f32="f64.promote_f32"
op_f32_demote_f64="f32.demote_f64"
op_f32_reinterpret_i32="f32.reinterpret_i32"
op_i32_reinterpret_f32="i32.reinterpret_f32"
op_f64_reinterpret_i64="f64.reinterpret_i64"
op_i64_reinterpret_f64="i64.reinterpret_f64"
op_select="select"
op_unreachable="unreachable"
op_memory_size="memory.size"
op_memory_grow="memory.grow"
op_type="type"
op_func="func"
op_param="param"
op_result="result"
op_local="local"
op_global="global"
op_module="module"
op_table="table"
op_memory="memory"
op_table="start"
op_elem="elem"
op_data="data"
op_offset="offset"
op_align="align="
op_import="import"
op_export="export"
op_register="register"
op_invoke="invoke"
op_get="get"
op_assert_malformed="assert_malformed"
op_assert_invalid="assert_invalid"
op_assert_unlinkable="assert_unlinkable"
op_assert_return="assert_return"
op_assert_trap="assert_trap"
op_assert_exhaustion="assert_exhaustion"
