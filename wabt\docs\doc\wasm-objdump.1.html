<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <style>
    table.head, table.foot { width: 100%; }
    td.head-rtitle, td.foot-os { text-align: right; }
    td.head-vol { text-align: center; }
    div.Pp { margin: 1ex 0ex; }
    div.Nd, div.Bf, div.Op { display: inline; }
    span.Pa, span.Ad { font-style: italic; }
    span.Ms { font-weight: bold; }
    dl.Bl-diag > dt { font-weight: bold; }
    code.Nm, code.Fl, code.Cm, code.Ic, code.In, code.Fd, code.Fn,
    code.Cd { font-weight: bold; font-family: inherit; }
  </style>
  <title>WABT(1)</title>
</head>
<body>
<table class="head">
  <tr>
    <td class="head-ltitle">WABT(1)</td>
    <td class="head-vol">General Commands Manual</td>
    <td class="head-rtitle">WABT(1)</td>
  </tr>
</table>
<div class="manual-text">
<section class="Sh">
<h1 class="Sh" id="NAME"><a class="permalink" href="#NAME">NAME</a></h1>
<code class="Nm">wasm-objdump</code> &#x2014;
<div class="Nd">print information about a wasm binary</div>
</section>
<section class="Sh">
<h1 class="Sh" id="SYNOPSIS"><a class="permalink" href="#SYNOPSIS">SYNOPSIS</a></h1>
<table class="Nm">
  <tr>
    <td><code class="Nm">wasm-objdump</code></td>
    <td>[options] <var class="Ar">file ...</var></td>
  </tr>
</table>
</section>
<section class="Sh">
<h1 class="Sh" id="DESCRIPTION"><a class="permalink" href="#DESCRIPTION">DESCRIPTION</a></h1>
<code class="Nm">wasm-objdump</code> prints information about a wasm binary,
  similar to objdump.
<p class="Pp">The options are as follows:</p>
<dl class="Bl-tag">
  <dt><a class="permalink" href="#h"><code class="Fl" id="h">-h</code></a>,
    <code class="Fl">-</code><code class="Fl">-headers</code></dt>
  <dd>Print headers</dd>
  <dt><a class="permalink" href="#j"><code class="Fl" id="j">-j</code></a>,
    <code class="Fl">-</code><code class="Fl">-section=SECTION</code></dt>
  <dd>Select just one section</dd>
  <dt><a class="permalink" href="#s"><code class="Fl" id="s">-s</code></a>,
    <code class="Fl">-</code><code class="Fl">-full-contents</code></dt>
  <dd>Print raw section contents</dd>
  <dt><a class="permalink" href="#d"><code class="Fl" id="d">-d</code></a>,
    <code class="Fl">-</code><code class="Fl">-disassemble</code></dt>
  <dd>Disassemble function bodies</dd>
  <dt><code class="Fl">-</code><code class="Fl">-debug</code></dt>
  <dd>Print extra debug information</dd>
  <dt><a class="permalink" href="#x"><code class="Fl" id="x">-x</code></a>,
    <code class="Fl">-</code><code class="Fl">-details</code></dt>
  <dd>Show section details</dd>
  <dt><a class="permalink" href="#r"><code class="Fl" id="r">-r</code></a>,
    <code class="Fl">-</code><code class="Fl">-reloc</code></dt>
  <dd>Show relocations inline with disassembly</dd>
  <dt><code class="Fl">-</code><code class="Fl">-help</code></dt>
  <dd>Print a help message</dd>
</dl>
</section>
<section class="Sh">
<h1 class="Sh" id="EXAMPLES"><a class="permalink" href="#EXAMPLES">EXAMPLES</a></h1>
<div class="Bd Bd-indent"><code class="Li">$ wasm-objdump test.wasm</code></div>
</section>
<section class="Sh">
<h1 class="Sh" id="SEE_ALSO"><a class="permalink" href="#SEE_ALSO">SEE
  ALSO</a></h1>
<a class="Xr" href="wasm-interp.1.html">wasm-interp(1)</a>,
  <a class="Xr" href="wasm-opcodecnt.1.html">wasm-opcodecnt(1)</a>,
  <a class="Xr" href="wasm-strip.1.html">wasm-strip(1)</a>,
  <a class="Xr" href="wasm-validate.1.html">wasm-validate(1)</a>,
  <a class="Xr" href="wasm2c.1.html">wasm2c(1)</a>,
  <a class="Xr" href="wasm2wat.1.html">wasm2wat(1)</a>,
  <a class="Xr" href="wast2json.1.html">wast2json(1)</a>,
  <a class="Xr" href="wat-desugar.1.html">wat-desugar(1)</a>,
  <a class="Xr" href="wat2wasm.1.html">wat2wasm(1)</a>,
  <a class="Xr" href="spectest-interp.1.html">spectest-interp(1)</a>
</section>
<section class="Sh">
<h1 class="Sh" id="BUGS"><a class="permalink" href="#BUGS">BUGS</a></h1>
If you find a bug, please report it at
<br/>
<a class="Lk" href="https://github.com/WebAssembly/wabt/issues">https://github.com/WebAssembly/wabt/issues</a>.
</section>
</div>
<table class="foot">
  <tr>
    <td class="foot-date">October 7, 2021</td>
    <td class="foot-os">Debian</td>
  </tr>
</table>
</body>
</html>
