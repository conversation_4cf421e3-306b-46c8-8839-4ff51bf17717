.Dd $Mdocdate$
.Dt WABT 1
.Os
.Sh NAME
.Nm wat2wasm
.Nd translate from WebAssembly text format to the WebAssembly binary format
.Sh SYNOPSIS
.Nm wat2wasm
.Op options
.Ar file
.Sh DESCRIPTION
.Nm
translates from WebAssembly text format to the WebAssembly binary format.
.Pp
The options are as follows:
.Bl -tag -width Ds
.It Fl v , Fl Fl verbose
Use multiple times for more info
.It Fl Fl help
Print a help message
.It Fl Fl debug-parser
Turn on debugging the parser of wat files
.It Fl d , Fl Fl dump-module
Print a hexdump of the module to stdout
.It Fl Fl enable-exceptions
Experimental exception handling
.It Fl Fl disable-mutable-globals
Import/export mutable globals
.It Fl Fl enable-saturating-float-to-int
Saturating float-to-int operators
.It Fl Fl enable-sign-extension
Sign-extension operators
.It Fl Fl disable-simd
SIMD support
.It Fl Fl enable-threads
Threading support
.It Fl o , Fl Fl output=FILE
output wasm binary file
.It Fl r , Fl Fl relocatable
Create a relocatable wasm binary (suitable for linking with e.g. lld)
.It Fl Fl no-canonicalize-leb128s
Write all LEB128 sizes as 5-bytes instead of their minimal size
.It Fl Fl debug-names
Write debug names to the generated binary file
.It Fl Fl no-check
Don't check for invalid modules
.El
.Sh EXAMPLES
Parse and typecheck test.wat
.Pp
.Dl $ wat2wasm test.wat
.Pp
parse test.wat and write to binary file test.wasm
.Pp
.Dl $ wat2wasm test.wat -o test.wasm
.Pp
Parse spec-test.wast, and write verbose output to stdout (including the meaning of every byte)
.Pp
.Dl $ wat2wasm spec-test.wast -v
.Sh SEE ALSO
.Xr wasm-interp 1 ,
.Xr wasm-objdump 1 ,
.Xr wasm-opcodecnt 1 ,
.Xr wasm-strip 1 ,
.Xr wasm-validate 1 ,
.Xr wasm2c 1 ,
.Xr wasm2wat 1 ,
.Xr wast2json 1 ,
.Xr wat-desugar 1 ,
.Xr spectest-interp 1
.Sh BUGS
If you find a bug, please report it at
.br
.Lk https://github.com/WebAssembly/wabt/issues .
