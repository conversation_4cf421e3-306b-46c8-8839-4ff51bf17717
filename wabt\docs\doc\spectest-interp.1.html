<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <style>
    table.head, table.foot { width: 100%; }
    td.head-rtitle, td.foot-os { text-align: right; }
    td.head-vol { text-align: center; }
    div.Pp { margin: 1ex 0ex; }
    div.Nd, div.Bf, div.Op { display: inline; }
    span.Pa, span.Ad { font-style: italic; }
    span.Ms { font-weight: bold; }
    dl.Bl-diag > dt { font-weight: bold; }
    code.Nm, code.Fl, code.Cm, code.Ic, code.In, code.Fd, code.Fn,
    code.Cd { font-weight: bold; font-family: inherit; }
  </style>
  <title>WABT(1)</title>
</head>
<body>
<table class="head">
  <tr>
    <td class="head-ltitle">WABT(1)</td>
    <td class="head-vol">General Commands Manual</td>
    <td class="head-rtitle">WABT(1)</td>
  </tr>
</table>
<div class="manual-text">
<section class="Sh">
<h1 class="Sh" id="NAME"><a class="permalink" href="#NAME">NAME</a></h1>
<code class="Nm">spectest-interp</code> &#x2014;
<div class="Nd">read a Spectest JSON file, and run its tests in the
  interpreter</div>
</section>
<section class="Sh">
<h1 class="Sh" id="SYNOPSIS"><a class="permalink" href="#SYNOPSIS">SYNOPSIS</a></h1>
<table class="Nm">
  <tr>
    <td><code class="Nm">spectest-interp</code></td>
    <td>[options] <var class="Ar">file</var></td>
  </tr>
</table>
</section>
<section class="Sh">
<h1 class="Sh" id="DESCRIPTION"><a class="permalink" href="#DESCRIPTION">DESCRIPTION</a></h1>
<code class="Nm">spectest-interp</code> reads a Spectest JSON file, and runs its
  tests in the interpreter.
<p class="Pp">The options are as follows:</p>
<dl class="Bl-tag">
  <dt><a class="permalink" href="#v"><code class="Fl" id="v">-v</code></a>,
    <code class="Fl">-</code><code class="Fl">-verbose</code></dt>
  <dd>Use multiple times for more info</dd>
  <dt><code class="Fl">-</code><code class="Fl">-help</code></dt>
  <dd>Print this help message</dd>
  <dt><code class="Fl">-</code><code class="Fl">-enable-exceptions</code></dt>
  <dd>Enable Experimental exception handling</dd>
  <dt><code class="Fl">-</code><code class="Fl">-disable-mutable-globals</code></dt>
  <dd>Disable Import/export mutable globals</dd>
  <dt><code class="Fl">-</code><code class="Fl">-enable-saturating-float-to-int</code></dt>
  <dd>Enable Saturating float-to-int operators</dd>
  <dt><code class="Fl">-</code><code class="Fl">-enable-sign-extension</code></dt>
  <dd>Enable Sign-extension operators</dd>
  <dt><code class="Fl">-</code><code class="Fl">-disable-simd</code></dt>
  <dd>Disable SIMD support</dd>
  <dt><code class="Fl">-</code><code class="Fl">-enable-threads</code></dt>
  <dd>Enable Threading support</dd>
  <dt><code class="Fl">-</code><code class="Fl">-enable-multi-value</code></dt>
  <dd>Enable Multi-value</dd>
  <dt><code class="Fl">-</code><code class="Fl">-enable-tail-call</code></dt>
  <dd>Enable Tail-call support</dd>
  <dt><a class="permalink" href="#V"><code class="Fl" id="V">-V</code></a>,
    <code class="Fl">-</code><code class="Fl">-value-stack-size=SIZE</code></dt>
  <dd>Size in elements of the value stack</dd>
  <dt><a class="permalink" href="#C"><code class="Fl" id="C">-C</code></a>,
    <code class="Fl">-</code><code class="Fl">-call-stack-size=SIZE</code></dt>
  <dd>Size in elements of the call stack</dd>
  <dt><a class="permalink" href="#t"><code class="Fl" id="t">-t</code></a>,
    <code class="Fl">-</code><code class="Fl">-trace</code></dt>
  <dd>Trace execution</dd>
</dl>
</section>
<section class="Sh">
<h1 class="Sh" id="EXAMPLES"><a class="permalink" href="#EXAMPLES">EXAMPLES</a></h1>
Parse test.json and run the spec tests
<p class="Pp"></p>
<div class="Bd Bd-indent"><code class="Li">$ spectest-interp
  test.json</code></div>
</section>
<section class="Sh">
<h1 class="Sh" id="SEE_ALSO"><a class="permalink" href="#SEE_ALSO">SEE
  ALSO</a></h1>
<a class="Xr" href="wasm-interp.1.html">wasm-interp(1)</a>,
  <a class="Xr" href="wasm-objdump.1.html">wasm-objdump(1)</a>,
  <a class="Xr" href="wasm-opcodecnt.1.html">wasm-opcodecnt(1)</a>,
  <a class="Xr" href="wasm-strip.1.html">wasm-strip(1)</a>,
  <a class="Xr" href="wasm-validate.1.html">wasm-validate(1)</a>,
  <a class="Xr" href="wasm2c.1.html">wasm2c(1)</a>,
  <a class="Xr" href="wasm2wat.1.html">wasm2wat(1)</a>,
  <a class="Xr" href="wast2json.1.html">wast2json(1)</a>,
  <a class="Xr" href="wat-desugar.1.html">wat-desugar(1)</a>,
  <a class="Xr" href="wat2wasm.1.html">wat2wasm(1)</a>
</section>
<section class="Sh">
<h1 class="Sh" id="BUGS"><a class="permalink" href="#BUGS">BUGS</a></h1>
If you find a bug, please report it at
<br/>
<a class="Lk" href="https://github.com/WebAssembly/wabt/issues">https://github.com/WebAssembly/wabt/issues</a>.
</section>
</div>
<table class="foot">
  <tr>
    <td class="foot-date">October 7, 2021</td>
    <td class="foot-os">Debian</td>
  </tr>
</table>
</body>
</html>
