;;; TOOL: run-gen-wasm
;;; ARGS: -v
magic
version
section("foo") { count[4] }
section(TYPE) { count[1] function params[0] results[1] i32 }
section("bar") { count[5] }
section("foo") { count[6] }
(;; STDERR ;;;
BeginModule(version: 1)
  BeginCustomSection('foo', size: 5)
  EndCustomSection
  BeginTypeSection(5)
    OnTypeCount(1)
    OnFuncType(index: 0, params: [], results: [i32])
  EndTypeSection
  BeginCustomSection('bar', size: 5)
  EndCustomSection
  BeginCustomSection('foo', size: 5)
  EndCustomSection
EndModule
;;; STDERR ;;)
(;; STDOUT ;;;
(module
  (type (;0;) (func (result i32))))
;;; STDOUT ;;)
