;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    block $outer 
      loop $inner
        ;; 1..64
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop

        ;; 65..128
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop


        ;; 129..192
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop

        ;; 193..256
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop
        nop nop nop nop nop nop nop nop


      ;; 257..258
      br $outer  ;; depth 2
      br $inner  ;; depth 1
      br 1       ;; depth 2 (due to implicit block)
      br 0       ;; depth 1 (due to implicit block)
      end
    end))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 00                                        ; local decl count
0000017: 02                                        ; block
0000018: 40                                        ; void
0000019: 03                                        ; loop
000001a: 40                                        ; void
000001b: 01                                        ; nop
000001c: 01                                        ; nop
000001d: 01                                        ; nop
000001e: 01                                        ; nop
000001f: 01                                        ; nop
0000020: 01                                        ; nop
0000021: 01                                        ; nop
0000022: 01                                        ; nop
0000023: 01                                        ; nop
0000024: 01                                        ; nop
0000025: 01                                        ; nop
0000026: 01                                        ; nop
0000027: 01                                        ; nop
0000028: 01                                        ; nop
0000029: 01                                        ; nop
000002a: 01                                        ; nop
000002b: 01                                        ; nop
000002c: 01                                        ; nop
000002d: 01                                        ; nop
000002e: 01                                        ; nop
000002f: 01                                        ; nop
0000030: 01                                        ; nop
0000031: 01                                        ; nop
0000032: 01                                        ; nop
0000033: 01                                        ; nop
0000034: 01                                        ; nop
0000035: 01                                        ; nop
0000036: 01                                        ; nop
0000037: 01                                        ; nop
0000038: 01                                        ; nop
0000039: 01                                        ; nop
000003a: 01                                        ; nop
000003b: 01                                        ; nop
000003c: 01                                        ; nop
000003d: 01                                        ; nop
000003e: 01                                        ; nop
000003f: 01                                        ; nop
0000040: 01                                        ; nop
0000041: 01                                        ; nop
0000042: 01                                        ; nop
0000043: 01                                        ; nop
0000044: 01                                        ; nop
0000045: 01                                        ; nop
0000046: 01                                        ; nop
0000047: 01                                        ; nop
0000048: 01                                        ; nop
0000049: 01                                        ; nop
000004a: 01                                        ; nop
000004b: 01                                        ; nop
000004c: 01                                        ; nop
000004d: 01                                        ; nop
000004e: 01                                        ; nop
000004f: 01                                        ; nop
0000050: 01                                        ; nop
0000051: 01                                        ; nop
0000052: 01                                        ; nop
0000053: 01                                        ; nop
0000054: 01                                        ; nop
0000055: 01                                        ; nop
0000056: 01                                        ; nop
0000057: 01                                        ; nop
0000058: 01                                        ; nop
0000059: 01                                        ; nop
000005a: 01                                        ; nop
000005b: 01                                        ; nop
000005c: 01                                        ; nop
000005d: 01                                        ; nop
000005e: 01                                        ; nop
000005f: 01                                        ; nop
0000060: 01                                        ; nop
0000061: 01                                        ; nop
0000062: 01                                        ; nop
0000063: 01                                        ; nop
0000064: 01                                        ; nop
0000065: 01                                        ; nop
0000066: 01                                        ; nop
0000067: 01                                        ; nop
0000068: 01                                        ; nop
0000069: 01                                        ; nop
000006a: 01                                        ; nop
000006b: 01                                        ; nop
000006c: 01                                        ; nop
000006d: 01                                        ; nop
000006e: 01                                        ; nop
000006f: 01                                        ; nop
0000070: 01                                        ; nop
0000071: 01                                        ; nop
0000072: 01                                        ; nop
0000073: 01                                        ; nop
0000074: 01                                        ; nop
0000075: 01                                        ; nop
0000076: 01                                        ; nop
0000077: 01                                        ; nop
0000078: 01                                        ; nop
0000079: 01                                        ; nop
000007a: 01                                        ; nop
000007b: 01                                        ; nop
000007c: 01                                        ; nop
000007d: 01                                        ; nop
000007e: 01                                        ; nop
000007f: 01                                        ; nop
0000080: 01                                        ; nop
0000081: 01                                        ; nop
0000082: 01                                        ; nop
0000083: 01                                        ; nop
0000084: 01                                        ; nop
0000085: 01                                        ; nop
0000086: 01                                        ; nop
0000087: 01                                        ; nop
0000088: 01                                        ; nop
0000089: 01                                        ; nop
000008a: 01                                        ; nop
000008b: 01                                        ; nop
000008c: 01                                        ; nop
000008d: 01                                        ; nop
000008e: 01                                        ; nop
000008f: 01                                        ; nop
0000090: 01                                        ; nop
0000091: 01                                        ; nop
0000092: 01                                        ; nop
0000093: 01                                        ; nop
0000094: 01                                        ; nop
0000095: 01                                        ; nop
0000096: 01                                        ; nop
0000097: 01                                        ; nop
0000098: 01                                        ; nop
0000099: 01                                        ; nop
000009a: 01                                        ; nop
000009b: 01                                        ; nop
000009c: 01                                        ; nop
000009d: 01                                        ; nop
000009e: 01                                        ; nop
000009f: 01                                        ; nop
00000a0: 01                                        ; nop
00000a1: 01                                        ; nop
00000a2: 01                                        ; nop
00000a3: 01                                        ; nop
00000a4: 01                                        ; nop
00000a5: 01                                        ; nop
00000a6: 01                                        ; nop
00000a7: 01                                        ; nop
00000a8: 01                                        ; nop
00000a9: 01                                        ; nop
00000aa: 01                                        ; nop
00000ab: 01                                        ; nop
00000ac: 01                                        ; nop
00000ad: 01                                        ; nop
00000ae: 01                                        ; nop
00000af: 01                                        ; nop
00000b0: 01                                        ; nop
00000b1: 01                                        ; nop
00000b2: 01                                        ; nop
00000b3: 01                                        ; nop
00000b4: 01                                        ; nop
00000b5: 01                                        ; nop
00000b6: 01                                        ; nop
00000b7: 01                                        ; nop
00000b8: 01                                        ; nop
00000b9: 01                                        ; nop
00000ba: 01                                        ; nop
00000bb: 01                                        ; nop
00000bc: 01                                        ; nop
00000bd: 01                                        ; nop
00000be: 01                                        ; nop
00000bf: 01                                        ; nop
00000c0: 01                                        ; nop
00000c1: 01                                        ; nop
00000c2: 01                                        ; nop
00000c3: 01                                        ; nop
00000c4: 01                                        ; nop
00000c5: 01                                        ; nop
00000c6: 01                                        ; nop
00000c7: 01                                        ; nop
00000c8: 01                                        ; nop
00000c9: 01                                        ; nop
00000ca: 01                                        ; nop
00000cb: 01                                        ; nop
00000cc: 01                                        ; nop
00000cd: 01                                        ; nop
00000ce: 01                                        ; nop
00000cf: 01                                        ; nop
00000d0: 01                                        ; nop
00000d1: 01                                        ; nop
00000d2: 01                                        ; nop
00000d3: 01                                        ; nop
00000d4: 01                                        ; nop
00000d5: 01                                        ; nop
00000d6: 01                                        ; nop
00000d7: 01                                        ; nop
00000d8: 01                                        ; nop
00000d9: 01                                        ; nop
00000da: 01                                        ; nop
00000db: 01                                        ; nop
00000dc: 01                                        ; nop
00000dd: 01                                        ; nop
00000de: 01                                        ; nop
00000df: 01                                        ; nop
00000e0: 01                                        ; nop
00000e1: 01                                        ; nop
00000e2: 01                                        ; nop
00000e3: 01                                        ; nop
00000e4: 01                                        ; nop
00000e5: 01                                        ; nop
00000e6: 01                                        ; nop
00000e7: 01                                        ; nop
00000e8: 01                                        ; nop
00000e9: 01                                        ; nop
00000ea: 01                                        ; nop
00000eb: 01                                        ; nop
00000ec: 01                                        ; nop
00000ed: 01                                        ; nop
00000ee: 01                                        ; nop
00000ef: 01                                        ; nop
00000f0: 01                                        ; nop
00000f1: 01                                        ; nop
00000f2: 01                                        ; nop
00000f3: 01                                        ; nop
00000f4: 01                                        ; nop
00000f5: 01                                        ; nop
00000f6: 01                                        ; nop
00000f7: 01                                        ; nop
00000f8: 01                                        ; nop
00000f9: 01                                        ; nop
00000fa: 01                                        ; nop
00000fb: 01                                        ; nop
00000fc: 01                                        ; nop
00000fd: 01                                        ; nop
00000fe: 01                                        ; nop
00000ff: 01                                        ; nop
0000100: 01                                        ; nop
0000101: 01                                        ; nop
0000102: 01                                        ; nop
0000103: 01                                        ; nop
0000104: 01                                        ; nop
0000105: 01                                        ; nop
0000106: 01                                        ; nop
0000107: 01                                        ; nop
0000108: 01                                        ; nop
0000109: 01                                        ; nop
000010a: 01                                        ; nop
000010b: 01                                        ; nop
000010c: 01                                        ; nop
000010d: 01                                        ; nop
000010e: 01                                        ; nop
000010f: 01                                        ; nop
0000110: 01                                        ; nop
0000111: 01                                        ; nop
0000112: 01                                        ; nop
0000113: 01                                        ; nop
0000114: 01                                        ; nop
0000115: 01                                        ; nop
0000116: 01                                        ; nop
0000117: 01                                        ; nop
0000118: 01                                        ; nop
0000119: 01                                        ; nop
000011a: 01                                        ; nop
000011b: 0c                                        ; br
000011c: 01                                        ; break depth
000011d: 0c                                        ; br
000011e: 00                                        ; break depth
000011f: 0c                                        ; br
0000120: 01                                        ; break depth
0000121: 0c                                        ; br
0000122: 00                                        ; break depth
0000123: 0b                                        ; end
0000124: 0b                                        ; end
0000125: 0b                                        ; end
; move data: [16, 126) -> [17, 127)
0000015: 9002                                      ; FIXUP func body size
; move data: [14, 127) -> [15, 128)
0000013: 9302                                      ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

loop-257-exprs-br.wasm:	file format wasm 0x1

Code Disassembly:

000018 func[0]:
 000019: 02 40                      | block
 00001b: 03 40                      |   loop
 00001d: 01                         |     nop
 00001e: 01                         |     nop
 00001f: 01                         |     nop
 000020: 01                         |     nop
 000021: 01                         |     nop
 000022: 01                         |     nop
 000023: 01                         |     nop
 000024: 01                         |     nop
 000025: 01                         |     nop
 000026: 01                         |     nop
 000027: 01                         |     nop
 000028: 01                         |     nop
 000029: 01                         |     nop
 00002a: 01                         |     nop
 00002b: 01                         |     nop
 00002c: 01                         |     nop
 00002d: 01                         |     nop
 00002e: 01                         |     nop
 00002f: 01                         |     nop
 000030: 01                         |     nop
 000031: 01                         |     nop
 000032: 01                         |     nop
 000033: 01                         |     nop
 000034: 01                         |     nop
 000035: 01                         |     nop
 000036: 01                         |     nop
 000037: 01                         |     nop
 000038: 01                         |     nop
 000039: 01                         |     nop
 00003a: 01                         |     nop
 00003b: 01                         |     nop
 00003c: 01                         |     nop
 00003d: 01                         |     nop
 00003e: 01                         |     nop
 00003f: 01                         |     nop
 000040: 01                         |     nop
 000041: 01                         |     nop
 000042: 01                         |     nop
 000043: 01                         |     nop
 000044: 01                         |     nop
 000045: 01                         |     nop
 000046: 01                         |     nop
 000047: 01                         |     nop
 000048: 01                         |     nop
 000049: 01                         |     nop
 00004a: 01                         |     nop
 00004b: 01                         |     nop
 00004c: 01                         |     nop
 00004d: 01                         |     nop
 00004e: 01                         |     nop
 00004f: 01                         |     nop
 000050: 01                         |     nop
 000051: 01                         |     nop
 000052: 01                         |     nop
 000053: 01                         |     nop
 000054: 01                         |     nop
 000055: 01                         |     nop
 000056: 01                         |     nop
 000057: 01                         |     nop
 000058: 01                         |     nop
 000059: 01                         |     nop
 00005a: 01                         |     nop
 00005b: 01                         |     nop
 00005c: 01                         |     nop
 00005d: 01                         |     nop
 00005e: 01                         |     nop
 00005f: 01                         |     nop
 000060: 01                         |     nop
 000061: 01                         |     nop
 000062: 01                         |     nop
 000063: 01                         |     nop
 000064: 01                         |     nop
 000065: 01                         |     nop
 000066: 01                         |     nop
 000067: 01                         |     nop
 000068: 01                         |     nop
 000069: 01                         |     nop
 00006a: 01                         |     nop
 00006b: 01                         |     nop
 00006c: 01                         |     nop
 00006d: 01                         |     nop
 00006e: 01                         |     nop
 00006f: 01                         |     nop
 000070: 01                         |     nop
 000071: 01                         |     nop
 000072: 01                         |     nop
 000073: 01                         |     nop
 000074: 01                         |     nop
 000075: 01                         |     nop
 000076: 01                         |     nop
 000077: 01                         |     nop
 000078: 01                         |     nop
 000079: 01                         |     nop
 00007a: 01                         |     nop
 00007b: 01                         |     nop
 00007c: 01                         |     nop
 00007d: 01                         |     nop
 00007e: 01                         |     nop
 00007f: 01                         |     nop
 000080: 01                         |     nop
 000081: 01                         |     nop
 000082: 01                         |     nop
 000083: 01                         |     nop
 000084: 01                         |     nop
 000085: 01                         |     nop
 000086: 01                         |     nop
 000087: 01                         |     nop
 000088: 01                         |     nop
 000089: 01                         |     nop
 00008a: 01                         |     nop
 00008b: 01                         |     nop
 00008c: 01                         |     nop
 00008d: 01                         |     nop
 00008e: 01                         |     nop
 00008f: 01                         |     nop
 000090: 01                         |     nop
 000091: 01                         |     nop
 000092: 01                         |     nop
 000093: 01                         |     nop
 000094: 01                         |     nop
 000095: 01                         |     nop
 000096: 01                         |     nop
 000097: 01                         |     nop
 000098: 01                         |     nop
 000099: 01                         |     nop
 00009a: 01                         |     nop
 00009b: 01                         |     nop
 00009c: 01                         |     nop
 00009d: 01                         |     nop
 00009e: 01                         |     nop
 00009f: 01                         |     nop
 0000a0: 01                         |     nop
 0000a1: 01                         |     nop
 0000a2: 01                         |     nop
 0000a3: 01                         |     nop
 0000a4: 01                         |     nop
 0000a5: 01                         |     nop
 0000a6: 01                         |     nop
 0000a7: 01                         |     nop
 0000a8: 01                         |     nop
 0000a9: 01                         |     nop
 0000aa: 01                         |     nop
 0000ab: 01                         |     nop
 0000ac: 01                         |     nop
 0000ad: 01                         |     nop
 0000ae: 01                         |     nop
 0000af: 01                         |     nop
 0000b0: 01                         |     nop
 0000b1: 01                         |     nop
 0000b2: 01                         |     nop
 0000b3: 01                         |     nop
 0000b4: 01                         |     nop
 0000b5: 01                         |     nop
 0000b6: 01                         |     nop
 0000b7: 01                         |     nop
 0000b8: 01                         |     nop
 0000b9: 01                         |     nop
 0000ba: 01                         |     nop
 0000bb: 01                         |     nop
 0000bc: 01                         |     nop
 0000bd: 01                         |     nop
 0000be: 01                         |     nop
 0000bf: 01                         |     nop
 0000c0: 01                         |     nop
 0000c1: 01                         |     nop
 0000c2: 01                         |     nop
 0000c3: 01                         |     nop
 0000c4: 01                         |     nop
 0000c5: 01                         |     nop
 0000c6: 01                         |     nop
 0000c7: 01                         |     nop
 0000c8: 01                         |     nop
 0000c9: 01                         |     nop
 0000ca: 01                         |     nop
 0000cb: 01                         |     nop
 0000cc: 01                         |     nop
 0000cd: 01                         |     nop
 0000ce: 01                         |     nop
 0000cf: 01                         |     nop
 0000d0: 01                         |     nop
 0000d1: 01                         |     nop
 0000d2: 01                         |     nop
 0000d3: 01                         |     nop
 0000d4: 01                         |     nop
 0000d5: 01                         |     nop
 0000d6: 01                         |     nop
 0000d7: 01                         |     nop
 0000d8: 01                         |     nop
 0000d9: 01                         |     nop
 0000da: 01                         |     nop
 0000db: 01                         |     nop
 0000dc: 01                         |     nop
 0000dd: 01                         |     nop
 0000de: 01                         |     nop
 0000df: 01                         |     nop
 0000e0: 01                         |     nop
 0000e1: 01                         |     nop
 0000e2: 01                         |     nop
 0000e3: 01                         |     nop
 0000e4: 01                         |     nop
 0000e5: 01                         |     nop
 0000e6: 01                         |     nop
 0000e7: 01                         |     nop
 0000e8: 01                         |     nop
 0000e9: 01                         |     nop
 0000ea: 01                         |     nop
 0000eb: 01                         |     nop
 0000ec: 01                         |     nop
 0000ed: 01                         |     nop
 0000ee: 01                         |     nop
 0000ef: 01                         |     nop
 0000f0: 01                         |     nop
 0000f1: 01                         |     nop
 0000f2: 01                         |     nop
 0000f3: 01                         |     nop
 0000f4: 01                         |     nop
 0000f5: 01                         |     nop
 0000f6: 01                         |     nop
 0000f7: 01                         |     nop
 0000f8: 01                         |     nop
 0000f9: 01                         |     nop
 0000fa: 01                         |     nop
 0000fb: 01                         |     nop
 0000fc: 01                         |     nop
 0000fd: 01                         |     nop
 0000fe: 01                         |     nop
 0000ff: 01                         |     nop
 000100: 01                         |     nop
 000101: 01                         |     nop
 000102: 01                         |     nop
 000103: 01                         |     nop
 000104: 01                         |     nop
 000105: 01                         |     nop
 000106: 01                         |     nop
 000107: 01                         |     nop
 000108: 01                         |     nop
 000109: 01                         |     nop
 00010a: 01                         |     nop
 00010b: 01                         |     nop
 00010c: 01                         |     nop
 00010d: 01                         |     nop
 00010e: 01                         |     nop
 00010f: 01                         |     nop
 000110: 01                         |     nop
 000111: 01                         |     nop
 000112: 01                         |     nop
 000113: 01                         |     nop
 000114: 01                         |     nop
 000115: 01                         |     nop
 000116: 01                         |     nop
 000117: 01                         |     nop
 000118: 01                         |     nop
 000119: 01                         |     nop
 00011a: 01                         |     nop
 00011b: 01                         |     nop
 00011c: 01                         |     nop
 00011d: 0c 01                      |     br 1
 00011f: 0c 00                      |     br 0
 000121: 0c 01                      |     br 1
 000123: 0c 00                      |     br 0
 000125: 0b                         |   end
 000126: 0b                         | end
 000127: 0b                         | end
;;; STDOUT ;;)
