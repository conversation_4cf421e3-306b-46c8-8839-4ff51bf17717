;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (type (func (param i32)))
  (type (func (param i64)))
  (type (func (param f32)))
  (type (func (param f64)))

  (type (func (result i32)))
  (type (func (result i64)))
  (type (func (result f32)))
  (type (func (result f64)))

  (type (func (param i32) (result f64))))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 09                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 01                                        ; num params
000000d: 7f                                        ; i32
000000e: 00                                        ; num results
; func type 1
000000f: 60                                        ; func
0000010: 01                                        ; num params
0000011: 7e                                        ; i64
0000012: 00                                        ; num results
; func type 2
0000013: 60                                        ; func
0000014: 01                                        ; num params
0000015: 7d                                        ; f32
0000016: 00                                        ; num results
; func type 3
0000017: 60                                        ; func
0000018: 01                                        ; num params
0000019: 7c                                        ; f64
000001a: 00                                        ; num results
; func type 4
000001b: 60                                        ; func
000001c: 00                                        ; num params
000001d: 01                                        ; num results
000001e: 7f                                        ; i32
; func type 5
000001f: 60                                        ; func
0000020: 00                                        ; num params
0000021: 01                                        ; num results
0000022: 7e                                        ; i64
; func type 6
0000023: 60                                        ; func
0000024: 00                                        ; num params
0000025: 01                                        ; num results
0000026: 7d                                        ; f32
; func type 7
0000027: 60                                        ; func
0000028: 00                                        ; num params
0000029: 01                                        ; num results
000002a: 7c                                        ; f64
; func type 8
000002b: 60                                        ; func
000002c: 01                                        ; num params
000002d: 7f                                        ; i32
000002e: 01                                        ; num results
000002f: 7c                                        ; f64
0000009: 26                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

signatures.wasm:	file format wasm 0x1

Code Disassembly:

;;; STDOUT ;;)
