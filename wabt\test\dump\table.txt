;;; TOOL: run-objdump
;;; ARGS0: -v
;;; ARGS1: -x
(module
  (type $t (func (param i32)))
  (func (type $t))
  (func (param i32 i64))
  (func (result f64) 
    f64.const 0)
  (table 6 6 funcref)
  (elem (i32.const 0) 1 1)
  (elem (i32.const 2) 0 0 1 2))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 03                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 01                                        ; num params
000000d: 7f                                        ; i32
000000e: 00                                        ; num results
; func type 1
000000f: 60                                        ; func
0000010: 02                                        ; num params
0000011: 7f                                        ; i32
0000012: 7e                                        ; i64
0000013: 00                                        ; num results
; func type 2
0000014: 60                                        ; func
0000015: 00                                        ; num params
0000016: 01                                        ; num results
0000017: 7c                                        ; f64
0000009: 0e                                        ; FIXUP section size
; section "Function" (3)
0000018: 03                                        ; section code
0000019: 00                                        ; section size (guess)
000001a: 03                                        ; num functions
000001b: 00                                        ; function 0 signature index
000001c: 01                                        ; function 1 signature index
000001d: 02                                        ; function 2 signature index
0000019: 04                                        ; FIXUP section size
; section "Table" (4)
000001e: 04                                        ; section code
000001f: 00                                        ; section size (guess)
0000020: 01                                        ; num tables
; table 0
0000021: 70                                        ; funcref
0000022: 01                                        ; limits: flags
0000023: 06                                        ; limits: initial
0000024: 06                                        ; limits: max
000001f: 05                                        ; FIXUP section size
; section "Elem" (9)
0000025: 09                                        ; section code
0000026: 00                                        ; section size (guess)
0000027: 02                                        ; num elem segments
; elem segment header 0
0000028: 00                                        ; segment flags
0000029: 41                                        ; i32.const
000002a: 00                                        ; i32 literal
000002b: 0b                                        ; end
000002c: 02                                        ; num elems
000002d: 01                                        ; elem function index
000002e: 01                                        ; elem function index
; elem segment header 1
000002f: 00                                        ; segment flags
0000030: 41                                        ; i32.const
0000031: 02                                        ; i32 literal
0000032: 0b                                        ; end
0000033: 04                                        ; num elems
0000034: 00                                        ; elem function index
0000035: 00                                        ; elem function index
0000036: 01                                        ; elem function index
0000037: 02                                        ; elem function index
0000026: 11                                        ; FIXUP section size
; section "Code" (10)
0000038: 0a                                        ; section code
0000039: 00                                        ; section size (guess)
000003a: 03                                        ; num functions
; function body 0
000003b: 00                                        ; func body size (guess)
000003c: 00                                        ; local decl count
000003d: 0b                                        ; end
000003b: 02                                        ; FIXUP func body size
; function body 1
000003e: 00                                        ; func body size (guess)
000003f: 00                                        ; local decl count
0000040: 0b                                        ; end
000003e: 02                                        ; FIXUP func body size
; function body 2
0000041: 00                                        ; func body size (guess)
0000042: 00                                        ; local decl count
0000043: 44                                        ; f64.const
0000044: 0000 0000 0000 0000                       ; f64 literal
000004c: 0b                                        ; end
0000041: 0b                                        ; FIXUP func body size
0000039: 13                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

table.wasm:	file format wasm 0x1

Section Details:

Type[3]:
 - type[0] (i32) -> nil
 - type[1] (i32, i64) -> nil
 - type[2] () -> f64
Function[3]:
 - func[0] sig=0
 - func[1] sig=1
 - func[2] sig=2
Table[1]:
 - table[0] type=funcref initial=6 max=6
Elem[2]:
 - segment[0] flags=0 table=0 count=2 - init i32=0
  - elem[0] = func[1]
  - elem[1] = func[1]
 - segment[1] flags=0 table=0 count=4 - init i32=2
  - elem[2] = func[0]
  - elem[3] = func[0]
  - elem[4] = func[1]
  - elem[5] = func[2]
Code[3]:
 - func[0] size=2
 - func[1] size=2
 - func[2] size=11

Code Disassembly:

00003c func[0]:
 00003d: 0b                         | end
00003f func[1]:
 000040: 0b                         | end
000042 func[2]:
 000043: 44 00 00 00 00 00 00 00 00 | f64.const 0x0p+0
 00004c: 0b                         | end
;;; STDOUT ;;)
