;;; TOOL: run-objdump

(module
  ;; i8x16
  (func (export  "func_i8x16_splat_0") (result  v128)
    i32.const 0x7f
    i8x16.splat)

  ;; i16x8
  (func (export  "func_i16x8_splat_0") (result  v128)
    i32.const 0x1234
    i16x8.splat)

  ;; i32x4
  (func (export  "func_i32x4_splat_0") (result  v128)
    i32.const 0x12345678
    i32x4.splat)

  ;; i64x2
  (func (export  "func_i64x2_splat_0") (result  v128)
    i64.const 0x1234567800000000
    i64x2.splat)

  ;; f32x4
  (func (export  "func_f32x4_splat_0") (result  v128)
    f32.const 1.0
    f32x4.splat)
  ;; float 6.91 == Hex 0x40dd1eb8

  ;; f64x2
  (func (export  "func_f64x2_splat_0") (result  v128)
    f64.const 1.0
    f64x2.splat)
)
(;; STDOUT ;;;

simd-splat.wasm:	file format wasm 0x1

Code Disassembly:

00009d func[0] <func_i8x16_splat_0>:
 00009e: 41 ff 00                   | i32.const 127
 0000a1: fd 0f                      | i8x16.splat
 0000a3: 0b                         | end
0000a5 func[1] <func_i16x8_splat_0>:
 0000a6: 41 b4 24                   | i32.const 4660
 0000a9: fd 10                      | i16x8.splat
 0000ab: 0b                         | end
0000ad func[2] <func_i32x4_splat_0>:
 0000ae: 41 f8 ac d1 91 01          | i32.const 305419896
 0000b4: fd 11                      | i32x4.splat
 0000b6: 0b                         | end
0000b8 func[3] <func_i64x2_splat_0>:
 0000b9: 42 80 80 80 80 80 cf 95 9a | i64.const 1311768464867721216
 0000c2: 12                         | 
 0000c3: fd 12                      | i64x2.splat
 0000c5: 0b                         | end
0000c7 func[4] <func_f32x4_splat_0>:
 0000c8: 43 00 00 80 3f             | f32.const 0x1p+0
 0000cd: fd 13                      | f32x4.splat
 0000cf: 0b                         | end
0000d1 func[5] <func_f64x2_splat_0>:
 0000d2: 44 00 00 00 00 00 00 f0 3f | f64.const 0x1p+0
 0000db: fd 14                      | f64x2.splat
 0000dd: 0b                         | end
;;; STDOUT ;;)
