;;; TOOL: run-objdump
;;; ARGS0: -v --debug-names
;;; ARGS1: --headers -x
(module
  (import "bar" "foo" (func $foo)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Import" (2)
000000e: 02                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num imports
; import header 0
0000011: 03                                        ; string length
0000012: 6261 72                                  bar  ; import module name
0000015: 03                                        ; string length
0000016: 666f 6f                                  foo  ; import field name
0000019: 00                                        ; import kind
000001a: 00                                        ; import signature index
000000f: 0b                                        ; FIXUP section size
; section "name"
000001b: 00                                        ; section code
000001c: 00                                        ; section size (guess)
000001d: 04                                        ; string length
000001e: 6e61 6d65                                name  ; custom section name
0000022: 01                                        ; name subsection type
0000023: 00                                        ; subsection size (guess)
0000024: 01                                        ; num names
0000025: 00                                        ; elem index
0000026: 03                                        ; string length
0000027: 666f 6f                                  foo  ; elem name 0
0000023: 06                                        ; FIXUP subsection size
000002a: 02                                        ; local name type
000002b: 00                                        ; subsection size (guess)
000002c: 01                                        ; num functions
000002d: 00                                        ; function index
000002e: 00                                        ; num locals
000002b: 03                                        ; FIXUP subsection size
000001c: 12                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

debug-import-names.wasm:	file format wasm 0x1

Sections:

     Type start=0x0000000a end=0x0000000e (size=0x00000004) count: 1
   Import start=0x00000010 end=0x0000001b (size=0x0000000b) count: 1
   Custom start=0x0000001d end=0x0000002f (size=0x00000012) "name"

Section Details:

Type[1]:
 - type[0] () -> nil
Import[1]:
 - func[0] sig=0 <foo> <- bar.foo
Custom:
 - name: "name"
 - func[0] <foo>

Code Disassembly:

;;; STDOUT ;;)
