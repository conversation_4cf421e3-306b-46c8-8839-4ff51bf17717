;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    (local i32)
    i32.const 0
    local.tee 0
    drop))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 01                                        ; local decl count
0000017: 01                                        ; local type count
0000018: 7f                                        ; i32
0000019: 41                                        ; i32.const
000001a: 00                                        ; i32 literal
000001b: 22                                        ; local.tee
000001c: 00                                        ; local index
000001d: 1a                                        ; drop
000001e: 0b                                        ; end
0000015: 09                                        ; FIXUP func body size
0000013: 0b                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

local-tee.wasm:	file format wasm 0x1

Code Disassembly:

000016 func[0]:
 000017: 01 7f                      | local[0] type=i32
 000019: 41 00                      | i32.const 0
 00001b: 22 00                      | local.tee 0
 00001d: 1a                         | drop
 00001e: 0b                         | end
;;; STDOUT ;;)
