;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    f64.const 0x0p0
    drop
    f64.const 0x1234.5p6 
    drop
    f64.const 0xffffffffffffffffp20
    drop
    f64.const 0x1p1023
    drop
    f64.const 0x0.08p1023
    drop
    f64.const 0x2.46p+1020
    drop
    f64.const 0x0.ffffffffffp1023
    drop
    f64.const 0x0.7fffffffffffp1023
    drop
    f64.const 0x0.ffffffffffffffffp1023
    drop
    f64.const 0x1.ffffffffffffcp1023
    drop
    f64.const 0x1.ffffffffffffep1023
    drop
    f64.const 0xffffffffffff88p-1033
    drop
    f64.const 0xffffffffffff98p-1033
    drop
    f64.const 0xffffffffffffffp-1055
    drop
    f64.const 0x000000001.10000000000p0
    drop
    f64.const 0x1000000000.p4
    drop
    f64.const -0x1.ff01p1
    drop
  )
)
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 00                                        ; local decl count
0000017: 44                                        ; f64.const
0000018: 0000 0000 0000 0000                       ; f64 literal
0000020: 1a                                        ; drop
0000021: 44                                        ; f64.const
0000022: 0000 0000 5034 1241                       ; f64 literal
000002a: 1a                                        ; drop
000002b: 44                                        ; f64.const
000002c: 0000 0000 0000 3045                       ; f64 literal
0000034: 1a                                        ; drop
0000035: 44                                        ; f64.const
0000036: 0000 0000 0000 e07f                       ; f64 literal
000003e: 1a                                        ; drop
000003f: 44                                        ; f64.const
0000040: 0000 0000 0000 907f                       ; f64 literal
0000048: 1a                                        ; drop
0000049: 44                                        ; f64.const
000004a: 0000 0000 0030 c27f                       ; f64 literal
0000052: 1a                                        ; drop
0000053: 44                                        ; f64.const
0000054: 00e0 ffff ffff df7f                       ; f64 literal
000005c: 1a                                        ; drop
000005d: 44                                        ; f64.const
000005e: c0ff ffff ffff cf7f                       ; f64 literal
0000066: 1a                                        ; drop
0000067: 44                                        ; f64.const
0000068: 0000 0000 0000 e07f                       ; f64 literal
0000070: 1a                                        ; drop
0000071: 44                                        ; f64.const
0000072: fcff ffff ffff ef7f                       ; f64 literal
000007a: 1a                                        ; drop
000007b: 44                                        ; f64.const
000007c: feff ffff ffff ef7f                       ; f64 literal
0000084: 1a                                        ; drop
0000085: 44                                        ; f64.const
0000086: f1ff ffff ffff df02                       ; f64 literal
000008e: 1a                                        ; drop
000008f: 44                                        ; f64.const
0000090: f3ff ffff ffff df02                       ; f64 literal
0000098: 1a                                        ; drop
0000099: 44                                        ; f64.const
000009a: 0000 0000 0000 8001                       ; f64 literal
00000a2: 1a                                        ; drop
00000a3: 44                                        ; f64.const
00000a4: 0000 0000 0000 f13f                       ; f64 literal
00000ac: 1a                                        ; drop
00000ad: 44                                        ; f64.const
00000ae: 0000 0000 0000 7042                       ; f64 literal
00000b6: 1a                                        ; drop
00000b7: 44                                        ; f64.const
00000b8: 0000 0000 10f0 0fc0                       ; f64 literal
00000c0: 1a                                        ; drop
00000c1: 0b                                        ; end
; move data: [16, c2) -> [17, c3)
0000015: ac01                                      ; FIXUP func body size
; move data: [14, c3) -> [15, c4)
0000013: af01                                      ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

hexfloat_f64.wasm:	file format wasm 0x1

Code Disassembly:

000018 func[0]:
 000019: 44 00 00 00 00 00 00 00 00 | f64.const 0x0p+0
 000022: 1a                         | drop
 000023: 44 00 00 00 00 50 34 12 41 | f64.const 0x1.2345p+18
 00002c: 1a                         | drop
 00002d: 44 00 00 00 00 00 00 30 45 | f64.const 0x1p+84
 000036: 1a                         | drop
 000037: 44 00 00 00 00 00 00 e0 7f | f64.const 0x1p+1023
 000040: 1a                         | drop
 000041: 44 00 00 00 00 00 00 90 7f | f64.const 0x1p+1018
 00004a: 1a                         | drop
 00004b: 44 00 00 00 00 00 30 c2 7f | f64.const 0x1.23p+1021
 000054: 1a                         | drop
 000055: 44 00 e0 ff ff ff ff df 7f | f64.const 0x1.fffffffffep+1022
 00005e: 1a                         | drop
 00005f: 44 c0 ff ff ff ff ff cf 7f | f64.const 0x1.fffffffffffcp+1021
 000068: 1a                         | drop
 000069: 44 00 00 00 00 00 00 e0 7f | f64.const 0x1p+1023
 000072: 1a                         | drop
 000073: 44 fc ff ff ff ff ff ef 7f | f64.const 0x1.ffffffffffffcp+1023
 00007c: 1a                         | drop
 00007d: 44 fe ff ff ff ff ff ef 7f | f64.const 0x1.ffffffffffffep+1023
 000086: 1a                         | drop
 000087: 44 f1 ff ff ff ff ff df 02 | f64.const 0x1.ffffffffffff1p-978
 000090: 1a                         | drop
 000091: 44 f3 ff ff ff ff ff df 02 | f64.const 0x1.ffffffffffff3p-978
 00009a: 1a                         | drop
 00009b: 44 00 00 00 00 00 00 80 01 | f64.const 0x1p-999
 0000a4: 1a                         | drop
 0000a5: 44 00 00 00 00 00 00 f1 3f | f64.const 0x1.1p+0
 0000ae: 1a                         | drop
 0000af: 44 00 00 00 00 00 00 70 42 | f64.const 0x1p+40
 0000b8: 1a                         | drop
 0000b9: 44 00 00 00 00 10 f0 0f c0 | f64.const -0x1.ff01p+1
 0000c2: 1a                         | drop
 0000c3: 0b                         | end
;;; STDOUT ;;)
