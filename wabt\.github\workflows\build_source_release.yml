name: Build Source Release

# Trigger whenever a release is created
on:
  release:
    types:
      - created

permissions:
  contents: write

jobs:
  build:
    name: build
    runs-on: ubuntu-latest
    steps:
    - uses: actions/setup-python@v1
      with:
        python-version: '3.x'

    - uses: actions/checkout@v1
      with:
        submodules: true

    - name: archive
      id: archive
      run: |
        VERSION=${{ github.event.release.tag_name }}
        PKGNAME="wabt-$VERSION"
        mkdir -p /tmp/$PKGNAME
        mv * /tmp/$PKGNAME
        mv /tmp/$PKGNAME .
        TARBALL=$PKGNAME.tar.xz
        SHASUM=$PKGNAME.tar.xz.sha256
        tar cJf $TARBALL $PKGNAME
        $PKGNAME/scripts/sha256sum.py $TARBALL > $SHASUM
        echo "::set-output name=tarball::$TARBALL"
        echo "::set-output name=shasum::$SHASUM"

    - name: upload tarball
      uses: svenstaro/upload-release-action@v2
      with:
        repo_token: ${{ secrets.GITHUB_TOKEN }}
        file: ./${{ steps.archive.outputs.tarball }}
        asset_name: ${{ steps.archive.outputs.tarball }}
        tag: ${{ github.ref }}

    - name: upload shasum
      uses: svenstaro/upload-release-action@v2
      with:
        repo_token: ${{ secrets.GITHUB_TOKEN }}
        file: ./${{ steps.archive.outputs.shasum }}
        asset_name: ${{ steps.archive.outputs.shasum }}
        tag: ${{ github.ref }}
