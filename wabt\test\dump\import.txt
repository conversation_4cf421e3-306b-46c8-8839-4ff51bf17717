;;; TOOL: run-objdump
;;; ARGS0: -v
;;; ARGS1: -x
(module
  (import "ignored" "test" (func (param i32 i64 f32 f64)))
  (import "ignored" "test2" (func (param i32) (result i32)))
  (import "ignored" "testmem" (memory 0))
  (import "ignored" "testtable" (table 0 funcref))
  (import "ignored" "testtag" (tag (param i32)))
)
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 03                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 04                                        ; num params
000000d: 7f                                        ; i32
000000e: 7e                                        ; i64
000000f: 7d                                        ; f32
0000010: 7c                                        ; f64
0000011: 00                                        ; num results
; func type 1
0000012: 60                                        ; func
0000013: 01                                        ; num params
0000014: 7f                                        ; i32
0000015: 01                                        ; num results
0000016: 7f                                        ; i32
; func type 2
0000017: 60                                        ; func
0000018: 01                                        ; num params
0000019: 7f                                        ; i32
000001a: 00                                        ; num results
0000009: 11                                        ; FIXUP section size
; section "Import" (2)
000001b: 02                                        ; section code
000001c: 00                                        ; section size (guess)
000001d: 05                                        ; num imports
; import header 0
000001e: 07                                        ; string length
000001f: 6967 6e6f 7265 64                        ignored  ; import module name
0000026: 04                                        ; string length
0000027: 7465 7374                                test  ; import field name
000002b: 00                                        ; import kind
000002c: 00                                        ; import signature index
; import header 1
000002d: 07                                        ; string length
000002e: 6967 6e6f 7265 64                        ignored  ; import module name
0000035: 05                                        ; string length
0000036: 7465 7374 32                             test2  ; import field name
000003b: 00                                        ; import kind
000003c: 01                                        ; import signature index
; import header 2
000003d: 07                                        ; string length
000003e: 6967 6e6f 7265 64                        ignored  ; import module name
0000045: 07                                        ; string length
0000046: 7465 7374 6d65 6d                        testmem  ; import field name
000004d: 02                                        ; import kind
000004e: 00                                        ; limits: flags
000004f: 00                                        ; limits: initial
; import header 3
0000050: 07                                        ; string length
0000051: 6967 6e6f 7265 64                        ignored  ; import module name
0000058: 09                                        ; string length
0000059: 7465 7374 7461 626c 65                   testtable  ; import field name
0000062: 01                                        ; import kind
0000063: 70                                        ; funcref
0000064: 00                                        ; limits: flags
0000065: 00                                        ; limits: initial
; import header 4
0000066: 07                                        ; string length
0000067: 6967 6e6f 7265 64                        ignored  ; import module name
000006e: 07                                        ; string length
000006f: 7465 7374 7461 67                        testtag  ; import field name
0000076: 04                                        ; import kind
0000077: 00                                        ; tag attribute
0000078: 02                                        ; tag signature index
000001c: 5c                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

import.wasm:	file format wasm 0x1

Section Details:

Type[3]:
 - type[0] (i32, i64, f32, f64) -> nil
 - type[1] (i32) -> i32
 - type[2] (i32) -> nil
Import[5]:
 - func[0] sig=0 <ignored.test> <- ignored.test
 - func[1] sig=1 <ignored.test2> <- ignored.test2
 - memory[0] pages: initial=0 <- ignored.testmem
 - table[0] type=funcref initial=0 <- ignored.testtable
 - tag[0] sig=2 <ignored.testtag> <- ignored.testtag

Code Disassembly:

;;; STDOUT ;;)
