;;; TOOL: run-objdump

(module
  ;; i8x16 extract lane signed/unsigned
  (func (export  "func_i8x16_extract_lane_s_0") (result i32)
    v128.const i32x4 0x00000001 0x0000000f 0x000000ff 0x0000017f
    i8x16.extract_lane_s 8)
  (func (export  "func_i8x16_extract_lane_u_0") (result i32)
    v128.const i32x4 0x00000001 0x0000000f 0x000000ff 0x0000017f
    i8x16.extract_lane_u 8)

  ;; i16x8 extract lane signed/unsigned
  (func (export  "func_i16x8_extract_lane_s_0") (result i32)
    v128.const i32x4 0x00000001 0x0000000f 0x0000ffff 0x0000017f
    i16x8.extract_lane_s 4)
  (func (export  "func_i16x8_extract_lane_u_0") (result i32)
    v128.const i32x4 0x00000001 0x0000000f 0x0000ffff 0x0000017f
    i16x8.extract_lane_u 4)

  ;; i32x4 extract lane
  (func (export  "func_i32x4_extract_lane_0") (result i32)
    v128.const i32x4 0x00000001 0x0000000f 0x0000ffff 0x0000017f
    i32x4.extract_lane 2)

  ;; i64x2 extract lane
  (func (export  "func_i64x2_extract_lane_0") (result i64)
    v128.const i32x4 0x0000000f 0x00000000 0x0000ffff 0x0000017f
    i64x2.extract_lane 0)

  ;; f32x4 extract lane
  ;; For Floating num:
  ;; 1.5 = 0x3fc00000
  (func (export  "func_f32x4_extract_lane_0") (result f32)
    v128.const i32x4 0x00000001 0x3fc00000 0x0000ffff 0x0000017f
    f32x4.extract_lane 1)

  ;; f64x2 extract lane
  ;; For Double num:
  ;; 4.5 = 0x4012000000000000
  (func (export  "func_f64x2_extract_lane_0") (result f64)
    v128.const i32x4 0x00000000 0x40120000 0x0000ffff 0x0000017f
    f64x2.extract_lane 0)

  ;; i8x16 replace lane
  (func (export  "func_i8x16_replace_lane_0") (result v128)
    v128.const i32x4 0x00000001 0x0000000f 0x000000ff 0x0000017f
    i32.const 0xe5
    i8x16.replace_lane 8)

  ;; i16x8 replace lane
  (func (export  "func_i16x8_replace_lane_0") (result v128)
    v128.const i32x4 0x00000001 0x0000000f 0x0000ffff 0x0000017f
    i32.const 0xe5e6
    i16x8.replace_lane 4)

  ;; i32x4 replace lane
  (func (export  "func_i32x4_replace_lane_0") (result v128)
    v128.const i32x4 0x00000001 0x0000000f 0x0000ffff 0x0000017f
    i32.const 0x12345678
    i32x4.replace_lane 2)

  ;; i64x2 replace lane
  (func (export  "func_i64x2_replace_lane_0") (result v128)
    v128.const i32x4 0x0000000f 0x00000000 0x0000ffff 0x0000017f
    i64.const 0x0000123400005678
    i64x2.replace_lane 0)

  ;; f32x4 replace lane
  (func (export  "func_f32x4_replace_lane_0") (result v128)
    v128.const i32x4 0x00000001 0x00000000 0x0000ffff 0x0000017f
    f32.const 1.5
    f32x4.replace_lane 1)

  ;; f64x2 replace lane
  (func (export  "func_f64x2_replace_lane_0") (result v128)
    v128.const i32x4 0x0000789a 0xff880330 0x0000ffff 0x0000017f
    f64.const 4.5
    f64x2.replace_lane 0)

  ;; v8x16 swizzle
  (func (export  "func_v8x16_swizzle_0") (result v128)
    v128.const i32x4 0x11223344 0x55667788 0x99aabbcc 0xddeeff00
    v128.const i32x4 0x0f0e0d0c 0x0b0a0908 0x07060504 0x03020100
    i8x16.swizzle)

  ;; v8x16 shuffle
  (func (export  "func_v8x16_shuffle_0") (result v128)
    v128.const i32x4 0xff00ff01 0xff00ff0f 0xff00ffff 0xff00ff7f
    v128.const i32x4 0x00550055 0x00550055 0x00550055 0x00550155
    i8x16.shuffle 16 1 18 3 20 5 22 7 24 9 26 11 28 13 30 15)
)
(;; STDOUT ;;;

simd-lane.wasm:	file format wasm 0x1

Code Disassembly:

0001f9 func[0] <func_i8x16_extract_lane_s_0>:
 0001fa: fd 0c 01 00 00 00 0f 00 00 | v128.const 0x00000001 0x0000000f 0x000000ff 0x0000017f
 000203: 00 ff 00 00 00 7f 01 00 00 | 
 00020c: fd 15 08                   | i8x16.extract_lane_s 8
 00020f: 0b                         | end
000211 func[1] <func_i8x16_extract_lane_u_0>:
 000212: fd 0c 01 00 00 00 0f 00 00 | v128.const 0x00000001 0x0000000f 0x000000ff 0x0000017f
 00021b: 00 ff 00 00 00 7f 01 00 00 | 
 000224: fd 16 08                   | i8x16.extract_lane_u 8
 000227: 0b                         | end
000229 func[2] <func_i16x8_extract_lane_s_0>:
 00022a: fd 0c 01 00 00 00 0f 00 00 | v128.const 0x00000001 0x0000000f 0x0000ffff 0x0000017f
 000233: 00 ff ff 00 00 7f 01 00 00 | 
 00023c: fd 18 04                   | i16x8.extract_lane_s 4
 00023f: 0b                         | end
000241 func[3] <func_i16x8_extract_lane_u_0>:
 000242: fd 0c 01 00 00 00 0f 00 00 | v128.const 0x00000001 0x0000000f 0x0000ffff 0x0000017f
 00024b: 00 ff ff 00 00 7f 01 00 00 | 
 000254: fd 19 04                   | i16x8.extract_lane_u 4
 000257: 0b                         | end
000259 func[4] <func_i32x4_extract_lane_0>:
 00025a: fd 0c 01 00 00 00 0f 00 00 | v128.const 0x00000001 0x0000000f 0x0000ffff 0x0000017f
 000263: 00 ff ff 00 00 7f 01 00 00 | 
 00026c: fd 1b 02                   | i32x4.extract_lane 2
 00026f: 0b                         | end
000271 func[5] <func_i64x2_extract_lane_0>:
 000272: fd 0c 0f 00 00 00 00 00 00 | v128.const 0x0000000f 0x00000000 0x0000ffff 0x0000017f
 00027b: 00 ff ff 00 00 7f 01 00 00 | 
 000284: fd 1d 00                   | i64x2.extract_lane 0
 000287: 0b                         | end
000289 func[6] <func_f32x4_extract_lane_0>:
 00028a: fd 0c 01 00 00 00 00 00 c0 | v128.const 0x00000001 0x3fc00000 0x0000ffff 0x0000017f
 000293: 3f ff ff 00 00 7f 01 00 00 | 
 00029c: fd 1f 01                   | f32x4.extract_lane 1
 00029f: 0b                         | end
0002a1 func[7] <func_f64x2_extract_lane_0>:
 0002a2: fd 0c 00 00 00 00 00 00 12 | v128.const 0x00000000 0x40120000 0x0000ffff 0x0000017f
 0002ab: 40 ff ff 00 00 7f 01 00 00 | 
 0002b4: fd 21 00                   | f64x2.extract_lane 0
 0002b7: 0b                         | end
0002b9 func[8] <func_i8x16_replace_lane_0>:
 0002ba: fd 0c 01 00 00 00 0f 00 00 | v128.const 0x00000001 0x0000000f 0x000000ff 0x0000017f
 0002c3: 00 ff 00 00 00 7f 01 00 00 | 
 0002cc: 41 e5 01                   | i32.const 229
 0002cf: fd 17 08                   | i8x16.replace_lane 8
 0002d2: 0b                         | end
0002d4 func[9] <func_i16x8_replace_lane_0>:
 0002d5: fd 0c 01 00 00 00 0f 00 00 | v128.const 0x00000001 0x0000000f 0x0000ffff 0x0000017f
 0002de: 00 ff ff 00 00 7f 01 00 00 | 
 0002e7: 41 e6 cb 03                | i32.const 58854
 0002eb: fd 1a 04                   | i16x8.replace_lane 4
 0002ee: 0b                         | end
0002f0 func[10] <func_i32x4_replace_lane_0>:
 0002f1: fd 0c 01 00 00 00 0f 00 00 | v128.const 0x00000001 0x0000000f 0x0000ffff 0x0000017f
 0002fa: 00 ff ff 00 00 7f 01 00 00 | 
 000303: 41 f8 ac d1 91 01          | i32.const 305419896
 000309: fd 1c 02                   | i32x4.replace_lane 2
 00030c: 0b                         | end
00030e func[11] <func_i64x2_replace_lane_0>:
 00030f: fd 0c 0f 00 00 00 00 00 00 | v128.const 0x0000000f 0x00000000 0x0000ffff 0x0000017f
 000318: 00 ff ff 00 00 7f 01 00 00 | 
 000321: 42 f8 ac 81 80 c0 c6 04    | i64.const 20014547621496
 000329: fd 1e 00                   | i64x2.replace_lane 0
 00032c: 0b                         | end
00032e func[12] <func_f32x4_replace_lane_0>:
 00032f: fd 0c 01 00 00 00 00 00 00 | v128.const 0x00000001 0x00000000 0x0000ffff 0x0000017f
 000338: 00 ff ff 00 00 7f 01 00 00 | 
 000341: 43 00 00 c0 3f             | f32.const 0x1.8p+0
 000346: fd 20 01                   | f32x4.replace_lane 1
 000349: 0b                         | end
00034b func[13] <func_f64x2_replace_lane_0>:
 00034c: fd 0c 9a 78 00 00 30 03 88 | v128.const 0x0000789a 0xff880330 0x0000ffff 0x0000017f
 000355: ff ff ff 00 00 7f 01 00 00 | 
 00035e: 44 00 00 00 00 00 00 12 40 | f64.const 0x1.2p+2
 000367: fd 22 00                   | f64x2.replace_lane 0
 00036a: 0b                         | end
00036c func[14] <func_v8x16_swizzle_0>:
 00036d: fd 0c 44 33 22 11 88 77 66 | v128.const 0x11223344 0x55667788 0x99aabbcc 0xddeeff00
 000376: 55 cc bb aa 99 00 ff ee dd | 
 00037f: fd 0c 0c 0d 0e 0f 08 09 0a | v128.const 0x0f0e0d0c 0x0b0a0908 0x07060504 0x03020100
 000388: 0b 04 05 06 07 00 01 02 03 | 
 000391: fd 0e                      | i8x16.swizzle
 000393: 0b                         | end
000395 func[15] <func_v8x16_shuffle_0>:
 000396: fd 0c 01 ff 00 ff 0f ff 00 | v128.const 0xff00ff01 0xff00ff0f 0xff00ffff 0xff00ff7f
 00039f: ff ff ff 00 ff 7f ff 00 ff | 
 0003a8: fd 0c 55 00 55 00 55 00 55 | v128.const 0x00550055 0x00550055 0x00550055 0x00550155
 0003b1: 00 55 00 55 00 55 01 55 00 | 
 0003ba: fd 0d 10 01 12 03 14 05 16 | i8x16.shuffle 0x03120110 0x07160514 0x0b1a0918 0x0f1e0d1c
 0003c3: 07 18 09 1a 0b 1c 0d 1e 0f | 
 0003cc: 0b                         | end
;;; STDOUT ;;)
