;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func {
    locals[decl_count[1] i32_count[2] i32]
    local.get 0
  }
}
section("name") {
  section(NAME_FUNCTION) {
    func_count[1]
    index[0]
    str("F0")
  }
  section(NAME_LOCALS) {
    func_count[1]
    index[0]
    local_count[2]
    index[0]
    str("L0")
    index[0]
    str("L1")
  }
}
(;; STDERR ;;;
0000035: error: duplicate local index: 0
0000035: error: duplicate local index: 0
;;; STDERR ;;)
