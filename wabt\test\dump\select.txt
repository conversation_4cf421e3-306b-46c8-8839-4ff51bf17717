;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    i32.const 2
    i32.const 3
    i32.const 1
    select
    drop
    i64.const 2
    i64.const 3
    i32.const 1
    select
    drop
    f32.const 2
    f32.const 3
    i32.const 1
    select (result f32)
    drop
    f64.const 2
    f64.const 3
    i32.const 1
    select
    drop))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 00                                        ; local decl count
0000017: 41                                        ; i32.const
0000018: 02                                        ; i32 literal
0000019: 41                                        ; i32.const
000001a: 03                                        ; i32 literal
000001b: 41                                        ; i32.const
000001c: 01                                        ; i32 literal
000001d: 1b                                        ; select
000001e: 1a                                        ; drop
000001f: 42                                        ; i64.const
0000020: 02                                        ; i64 literal
0000021: 42                                        ; i64.const
0000022: 03                                        ; i64 literal
0000023: 41                                        ; i32.const
0000024: 01                                        ; i32 literal
0000025: 1b                                        ; select
0000026: 1a                                        ; drop
0000027: 43                                        ; f32.const
0000028: 0000 0040                                 ; f32 literal
000002c: 43                                        ; f32.const
000002d: 0000 4040                                 ; f32 literal
0000031: 41                                        ; i32.const
0000032: 01                                        ; i32 literal
0000033: 1c                                        ; select
0000034: 01                                        ; num result types
0000035: 7d                                        ; result type
0000036: 1a                                        ; drop
0000037: 44                                        ; f64.const
0000038: 0000 0000 0000 0040                       ; f64 literal
0000040: 44                                        ; f64.const
0000041: 0000 0000 0000 0840                       ; f64 literal
0000049: 41                                        ; i32.const
000004a: 01                                        ; i32 literal
000004b: 1b                                        ; select
000004c: 1a                                        ; drop
000004d: 0b                                        ; end
0000015: 38                                        ; FIXUP func body size
0000013: 3a                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

select.wasm:	file format wasm 0x1

Code Disassembly:

000016 func[0]:
 000017: 41 02                      | i32.const 2
 000019: 41 03                      | i32.const 3
 00001b: 41 01                      | i32.const 1
 00001d: 1b                         | select
 00001e: 1a                         | drop
 00001f: 42 02                      | i64.const 2
 000021: 42 03                      | i64.const 3
 000023: 41 01                      | i32.const 1
 000025: 1b                         | select
 000026: 1a                         | drop
 000027: 43 00 00 00 40             | f32.const 0x1p+1
 00002c: 43 00 00 40 40             | f32.const 0x1.8p+1
 000031: 41 01                      | i32.const 1
 000033: 1c 01 7d                   | select f32
 000036: 1a                         | drop
 000037: 44 00 00 00 00 00 00 00 40 | f64.const 0x1p+1
 000040: 44 00 00 00 00 00 00 08 40 | f64.const 0x1.8p+1
 000049: 41 01                      | i32.const 1
 00004b: 1b                         | select
 00004c: 1a                         | drop
 00004d: 0b                         | end
;;; STDOUT ;;)
