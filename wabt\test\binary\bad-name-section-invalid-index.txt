;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func {
    locals[decl_count[1] i32_count[1] i32]
    local.get 0
  }
}
section("name") {
  section(NAME_FUNCTION) {
    func_count[1]
    index[8]
    str("$F0")
  }
}
(;; STDERR ;;;
0000028: error: invalid function index: 8
0000028: error: invalid function index: 8
;;; STDERR ;;)
