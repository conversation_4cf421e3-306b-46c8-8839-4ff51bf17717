;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func {
    locals[decl_count[1] i32_count[1] i32]
    local.get 0
  }
}
section("name") {
  subsection[1]
  length[1]  ;; length is too short
  func_count[1]
  index[0]
  str("$F0")
}
(;; STDERR ;;;
0000027: error: invalid name count 1, only 0 bytes left in section
0000027: error: invalid name count 1, only 0 bytes left in section
;;; STDERR ;;)
