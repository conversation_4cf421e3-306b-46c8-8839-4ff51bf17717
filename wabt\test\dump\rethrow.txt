;;; TOOL: run-objdump
;;; ARGS0: -v --enable-exceptions
(module
  (tag $e1)
  (func
    try
    catch $e1
      rethrow 0
    end))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Tag" (13)
0000012: 0d                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; tag count
; tag 0
0000015: 00                                        ; tag attribute
0000016: 00                                        ; tag signature index
0000013: 03                                        ; FIXUP section size
; section "Code" (10)
0000017: 0a                                        ; section code
0000018: 00                                        ; section size (guess)
0000019: 01                                        ; num functions
; function body 0
000001a: 00                                        ; func body size (guess)
000001b: 00                                        ; local decl count
000001c: 06                                        ; try
000001d: 40                                        ; void
000001e: 07                                        ; catch
000001f: 00                                        ; catch tag
0000020: 09                                        ; rethrow
0000021: 00                                        ; rethrow depth
0000022: 0b                                        ; end
0000023: 0b                                        ; end
000001a: 09                                        ; FIXUP func body size
0000018: 0b                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

rethrow.wasm:	file format wasm 0x1

Code Disassembly:

00001b func[0]:
 00001c: 06 40                      | try
 00001e: 07 00                      | catch 0
 000020: 09 00                      |   rethrow 0
 000022: 0b                         | end
 000023: 0b                         | end
;;; STDOUT ;;)
