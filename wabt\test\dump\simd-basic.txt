;;; TOOL: run-objdump

(module
  (func (export "main") (result v128)
    v128.const i32x4 0x00000001 0x00000002 0x00000003 0x00000004
    return))
(;; STDOUT ;;;

simd-basic.wasm:	file format wasm 0x1

Code Disassembly:

000021 func[0] <main>:
 000022: fd 0c 01 00 00 00 02 00 00 | v128.const 0x00000001 0x00000002 0x00000003 0x00000004
 00002b: 00 03 00 00 00 04 00 00 00 | 
 000034: 0f                         | return
 000035: 0b                         | end
;;; STDOUT ;;)
