;;; TOOL: run-objdump

(module
  ;; i8x16 eq
  (func (export "i8x16_eq_0") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    i8x16.eq)

  ;; i16x8 eq
  (func (export "i16x8_eq_0") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    i16x8.eq)

  ;; i32x4 eq
  (func (export "i32x4_eq_0") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    i32x4.eq)

  ;; f32x4 eq
  (func (export "f32x4_eq_0") (result v128)
    v128.const i32x4 0x00000000 0xffc00000 0x449a5000 0x449a5000
    v128.const i32x4 0x80000000 0xffc00000 0x449a5000 0x3f800000
    f32x4.eq)

  ;; f64x2 eq
  (func (export "f64x2_eq_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0x80000000 0x00000000 0xfff80000
    f64x2.eq)

  ;; i8x16 ne
  (func (export "i8x16_ne_0") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    i8x16.ne)

  ;; i16x8 ne
  (func (export "i16x8_ne_0") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    i16x8.ne)

  ;; i32x4 ne
  (func (export "i32x4_ne_0") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    i32x4.ne)

  ;; f32x4 ne
  (func (export "f32x4_ne_0") (result v128)
    v128.const i32x4 0x00000000 0xffc00000 0x449a5000 0x449a5000
    v128.const i32x4 0x80000000 0xffc00000 0x449a5000 0x3f800000
    f32x4.ne)

  ;; f64x2 ne
  (func (export "f64x2_ne_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0x80000000 0x00000000 0xfff80000
    f64x2.ne)

  ;; i8x16 lt (sign and unsigned)
  (func (export "i8x16_lt_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i8x16.lt_s)
  (func (export "i8x16_lt_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i8x16.lt_u)

  ;; i16x8 lt (sign and unsigned)
  (func (export "i16x8_lt_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i16x8.lt_s)
  (func (export "i16x8_lt_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i16x8.lt_u)

  ;; i32x4 lt (sign and unsigned)
  (func (export "i32x4_lt_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i32x4.lt_s)
  (func (export "i32x4_lt_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i32x4.lt_u)

  ;; f32x4 lt
  (func (export "f32x4_lt_0") (result v128)
    v128.const i32x4 0x00000000 0xffc00000 0x449a5000 0x449a5000
    v128.const i32x4 0x80000000 0xffc00000 0xffc00000 0x3f800000
    f32x4.lt)

  ;; f64x2 lt
  (func (export "f64x2_lt_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0x80000000 0x00000000 0xfff80000
    f64x2.lt)

  ;; i8x16 le (sign and unsigned)
  (func (export "i8x16_le_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i8x16.le_s)
  (func (export "i8x16_le_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i8x16.le_u)

  ;; i16x8 le (sign and unsigned)
  (func (export "i16x8_le_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i16x8.le_s)
  (func (export "i16x8_le_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i16x8.le_u)

  ;; i32x4 le (sign and unsigned)
  (func (export "i32x4_le_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i32x4.le_s)
  (func (export "i32x4_le_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i32x4.le_u)

  ;; f32x4 le
  (func (export "f32x4_le_0") (result v128)
    v128.const i32x4 0x00000000 0xffc00000 0x449a5000 0x449a5000
    v128.const i32x4 0x80000000 0xffc00000 0xffc00000 0x3f800000
    f32x4.le)

  ;; f64x2 le
  (func (export "f64x2_le_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0x80000000 0x00000000 0xfff80000
    f64x2.le)

  ;; i8x16 gt (sign and unsigned)
  (func (export "i8x16_gt_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i8x16.gt_s)
  (func (export "i8x16_gt_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i8x16.gt_u)

  ;; i16x8 gt (sign and unsigned)
  (func (export "i16x8_gt_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i16x8.gt_s)
  (func (export "i16x8_gt_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i16x8.gt_u)

  ;; i32x4 gt (sign and unsigned)
  (func (export "i32x4_gt_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i32x4.gt_s)
  (func (export "i32x4_gt_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i32x4.gt_u)

  ;; f32x4 gt
  (func (export "f32x4_gt_0") (result v128)
    v128.const i32x4 0x00000000 0xffc00000 0x449a5000 0x449a5000
    v128.const i32x4 0x80000000 0xffc00000 0xffc00000 0x3f800000
    f32x4.gt)

  ;; f64x2 gt
  (func (export "f64x2_gt_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0x80000000 0x00000000 0xfff80000
    f64x2.gt)

  ;; i8x16 ge (sign and unsigned)
  (func (export "i8x16_ge_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i8x16.ge_s)
  (func (export "i8x16_ge_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i8x16.ge_u)

  ;; i16x8 ge (sign and unsigned)
  (func (export "i16x8_ge_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i16x8.ge_s)
  (func (export "i16x8_ge_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i16x8.ge_u)

  ;; i32x4 ge (sign and unsigned)
  (func (export "i32x4_ge_s") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i32x4.ge_s)
  (func (export "i32x4_ge_u") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00008003 0x00000004
    v128.const i32x4 0x02000001 0xe000ff02 0x00000003 0x00008104
    i32x4.ge_u)

  ;; f32x4 ge
  (func (export "f32x4_ge_0") (result v128)
    v128.const i32x4 0x00000000 0xffc00000 0x449a5000 0x449a5000
    v128.const i32x4 0x80000000 0xffc00000 0xffc00000 0x3f800000
    f32x4.ge)

  ;; f64x2 ge
  (func (export "f64x2_ge_0") (result v128)
    v128.const i32x4 0x00000000 0x00000000 0x00000000 0xfff80000
    v128.const i32x4 0x00000000 0x80000000 0x00000000 0xfff80000
    f64x2.ge)
)
(;; STDOUT ;;;

simd-compare.wasm:	file format wasm 0x1

Code Disassembly:

000267 func[0] <i8x16_eq_0>:
 000268: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 000271: e0 03 00 00 00 04 00 00 00 | 
 00027a: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 000283: e0 03 00 00 00 04 00 00 00 | 
 00028c: fd 23                      | i8x16.eq
 00028e: 0b                         | end
000290 func[1] <i16x8_eq_0>:
 000291: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 00029a: e0 03 00 00 00 04 00 00 00 | 
 0002a3: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 0002ac: e0 03 00 00 00 04 00 00 00 | 
 0002b5: fd 2d                      | i16x8.eq
 0002b7: 0b                         | end
0002b9 func[2] <i32x4_eq_0>:
 0002ba: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 0002c3: e0 03 00 00 00 04 00 00 00 | 
 0002cc: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 0002d5: e0 03 00 00 00 04 00 00 00 | 
 0002de: fd 37                      | i32x4.eq
 0002e0: 0b                         | end
0002e2 func[3] <f32x4_eq_0>:
 0002e3: fd 0c 00 00 00 00 00 00 c0 | v128.const 0x00000000 0xffc00000 0x449a5000 0x449a5000
 0002ec: ff 00 50 9a 44 00 50 9a 44 | 
 0002f5: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x449a5000 0x3f800000
 0002fe: ff 00 50 9a 44 00 00 80 3f | 
 000307: fd 41                      | f32x4.eq
 000309: 0b                         | end
00030b func[4] <f64x2_eq_0>:
 00030c: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 000315: 00 00 00 00 00 00 00 f8 ff | 
 00031e: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x80000000 0x00000000 0xfff80000
 000327: 80 00 00 00 00 00 00 f8 ff | 
 000330: fd 47                      | f64x2.eq
 000332: 0b                         | end
000334 func[5] <i8x16_ne_0>:
 000335: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 00033e: e0 03 00 00 00 04 00 00 00 | 
 000347: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 000350: e0 03 00 00 00 04 00 00 00 | 
 000359: fd 24                      | i8x16.ne
 00035b: 0b                         | end
00035d func[6] <i16x8_ne_0>:
 00035e: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 000367: e0 03 00 00 00 04 00 00 00 | 
 000370: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 000379: e0 03 00 00 00 04 00 00 00 | 
 000382: fd 2e                      | i16x8.ne
 000384: 0b                         | end
000386 func[7] <i32x4_ne_0>:
 000387: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 000390: e0 03 00 00 00 04 00 00 00 | 
 000399: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 0003a2: e0 03 00 00 00 04 00 00 00 | 
 0003ab: fd 38                      | i32x4.ne
 0003ad: 0b                         | end
0003af func[8] <f32x4_ne_0>:
 0003b0: fd 0c 00 00 00 00 00 00 c0 | v128.const 0x00000000 0xffc00000 0x449a5000 0x449a5000
 0003b9: ff 00 50 9a 44 00 50 9a 44 | 
 0003c2: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0x449a5000 0x3f800000
 0003cb: ff 00 50 9a 44 00 00 80 3f | 
 0003d4: fd 42                      | f32x4.ne
 0003d6: 0b                         | end
0003d8 func[9] <f64x2_ne_0>:
 0003d9: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 0003e2: 00 00 00 00 00 00 00 f8 ff | 
 0003eb: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x80000000 0x00000000 0xfff80000
 0003f4: 80 00 00 00 00 00 00 f8 ff | 
 0003fd: fd 48                      | f64x2.ne
 0003ff: 0b                         | end
000401 func[10] <i8x16_lt_s>:
 000402: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 00040b: e0 03 80 00 00 04 00 00 00 | 
 000414: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 00041d: e0 03 00 00 00 04 81 00 00 | 
 000426: fd 25                      | i8x16.lt_s
 000428: 0b                         | end
00042a func[11] <i8x16_lt_u>:
 00042b: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 000434: e0 03 80 00 00 04 00 00 00 | 
 00043d: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000446: e0 03 00 00 00 04 81 00 00 | 
 00044f: fd 26                      | i8x16.lt_u
 000451: 0b                         | end
000453 func[12] <i16x8_lt_s>:
 000454: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 00045d: e0 03 80 00 00 04 00 00 00 | 
 000466: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 00046f: e0 03 00 00 00 04 81 00 00 | 
 000478: fd 2f                      | i16x8.lt_s
 00047a: 0b                         | end
00047c func[13] <i16x8_lt_u>:
 00047d: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 000486: e0 03 80 00 00 04 00 00 00 | 
 00048f: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000498: e0 03 00 00 00 04 81 00 00 | 
 0004a1: fd 30                      | i16x8.lt_u
 0004a3: 0b                         | end
0004a5 func[14] <i32x4_lt_s>:
 0004a6: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0004af: e0 03 80 00 00 04 00 00 00 | 
 0004b8: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0004c1: e0 03 00 00 00 04 81 00 00 | 
 0004ca: fd 39                      | i32x4.lt_s
 0004cc: 0b                         | end
0004ce func[15] <i32x4_lt_u>:
 0004cf: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0004d8: e0 03 80 00 00 04 00 00 00 | 
 0004e1: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0004ea: e0 03 00 00 00 04 81 00 00 | 
 0004f3: fd 3a                      | i32x4.lt_u
 0004f5: 0b                         | end
0004f7 func[16] <f32x4_lt_0>:
 0004f8: fd 0c 00 00 00 00 00 00 c0 | v128.const 0x00000000 0xffc00000 0x449a5000 0x449a5000
 000501: ff 00 50 9a 44 00 50 9a 44 | 
 00050a: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0xffc00000 0x3f800000
 000513: ff 00 00 c0 ff 00 00 80 3f | 
 00051c: fd 43                      | f32x4.lt
 00051e: 0b                         | end
000520 func[17] <f64x2_lt_0>:
 000521: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 00052a: 00 00 00 00 00 00 00 f8 ff | 
 000533: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x80000000 0x00000000 0xfff80000
 00053c: 80 00 00 00 00 00 00 f8 ff | 
 000545: fd 49                      | f64x2.lt
 000547: 0b                         | end
000549 func[18] <i8x16_le_s>:
 00054a: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 000553: e0 03 80 00 00 04 00 00 00 | 
 00055c: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000565: e0 03 00 00 00 04 81 00 00 | 
 00056e: fd 29                      | i8x16.le_s
 000570: 0b                         | end
000572 func[19] <i8x16_le_u>:
 000573: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 00057c: e0 03 80 00 00 04 00 00 00 | 
 000585: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 00058e: e0 03 00 00 00 04 81 00 00 | 
 000597: fd 2a                      | i8x16.le_u
 000599: 0b                         | end
00059b func[20] <i16x8_le_s>:
 00059c: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0005a5: e0 03 80 00 00 04 00 00 00 | 
 0005ae: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0005b7: e0 03 00 00 00 04 81 00 00 | 
 0005c0: fd 33                      | i16x8.le_s
 0005c2: 0b                         | end
0005c4 func[21] <i16x8_le_u>:
 0005c5: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0005ce: e0 03 80 00 00 04 00 00 00 | 
 0005d7: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0005e0: e0 03 00 00 00 04 81 00 00 | 
 0005e9: fd 34                      | i16x8.le_u
 0005eb: 0b                         | end
0005ed func[22] <i32x4_le_s>:
 0005ee: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0005f7: e0 03 80 00 00 04 00 00 00 | 
 000600: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000609: e0 03 00 00 00 04 81 00 00 | 
 000612: fd 3d                      | i32x4.le_s
 000614: 0b                         | end
000616 func[23] <i32x4_le_u>:
 000617: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 000620: e0 03 80 00 00 04 00 00 00 | 
 000629: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000632: e0 03 00 00 00 04 81 00 00 | 
 00063b: fd 3e                      | i32x4.le_u
 00063d: 0b                         | end
00063f func[24] <f32x4_le_0>:
 000640: fd 0c 00 00 00 00 00 00 c0 | v128.const 0x00000000 0xffc00000 0x449a5000 0x449a5000
 000649: ff 00 50 9a 44 00 50 9a 44 | 
 000652: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0xffc00000 0x3f800000
 00065b: ff 00 00 c0 ff 00 00 80 3f | 
 000664: fd 45                      | f32x4.le
 000666: 0b                         | end
000668 func[25] <f64x2_le_0>:
 000669: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 000672: 00 00 00 00 00 00 00 f8 ff | 
 00067b: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x80000000 0x00000000 0xfff80000
 000684: 80 00 00 00 00 00 00 f8 ff | 
 00068d: fd 4b                      | f64x2.le
 00068f: 0b                         | end
000691 func[26] <i8x16_gt_s>:
 000692: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 00069b: e0 03 80 00 00 04 00 00 00 | 
 0006a4: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0006ad: e0 03 00 00 00 04 81 00 00 | 
 0006b6: fd 27                      | i8x16.gt_s
 0006b8: 0b                         | end
0006ba func[27] <i8x16_gt_u>:
 0006bb: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0006c4: e0 03 80 00 00 04 00 00 00 | 
 0006cd: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0006d6: e0 03 00 00 00 04 81 00 00 | 
 0006df: fd 28                      | i8x16.gt_u
 0006e1: 0b                         | end
0006e3 func[28] <i16x8_gt_s>:
 0006e4: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0006ed: e0 03 80 00 00 04 00 00 00 | 
 0006f6: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0006ff: e0 03 00 00 00 04 81 00 00 | 
 000708: fd 31                      | i16x8.gt_s
 00070a: 0b                         | end
00070c func[29] <i16x8_gt_u>:
 00070d: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 000716: e0 03 80 00 00 04 00 00 00 | 
 00071f: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000728: e0 03 00 00 00 04 81 00 00 | 
 000731: fd 32                      | i16x8.gt_u
 000733: 0b                         | end
000735 func[30] <i32x4_gt_s>:
 000736: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 00073f: e0 03 80 00 00 04 00 00 00 | 
 000748: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000751: e0 03 00 00 00 04 81 00 00 | 
 00075a: fd 3b                      | i32x4.gt_s
 00075c: 0b                         | end
00075e func[31] <i32x4_gt_u>:
 00075f: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 000768: e0 03 80 00 00 04 00 00 00 | 
 000771: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 00077a: e0 03 00 00 00 04 81 00 00 | 
 000783: fd 3c                      | i32x4.gt_u
 000785: 0b                         | end
000787 func[32] <f32x4_gt_0>:
 000788: fd 0c 00 00 00 00 00 00 c0 | v128.const 0x00000000 0xffc00000 0x449a5000 0x449a5000
 000791: ff 00 50 9a 44 00 50 9a 44 | 
 00079a: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0xffc00000 0x3f800000
 0007a3: ff 00 00 c0 ff 00 00 80 3f | 
 0007ac: fd 44                      | f32x4.gt
 0007ae: 0b                         | end
0007b0 func[33] <f64x2_gt_0>:
 0007b1: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 0007ba: 00 00 00 00 00 00 00 f8 ff | 
 0007c3: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x80000000 0x00000000 0xfff80000
 0007cc: 80 00 00 00 00 00 00 f8 ff | 
 0007d5: fd 4a                      | f64x2.gt
 0007d7: 0b                         | end
0007d9 func[34] <i8x16_ge_s>:
 0007da: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0007e3: e0 03 80 00 00 04 00 00 00 | 
 0007ec: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0007f5: e0 03 00 00 00 04 81 00 00 | 
 0007fe: fd 2b                      | i8x16.ge_s
 000800: 0b                         | end
000802 func[35] <i8x16_ge_u>:
 000803: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 00080c: e0 03 80 00 00 04 00 00 00 | 
 000815: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 00081e: e0 03 00 00 00 04 81 00 00 | 
 000827: fd 2c                      | i8x16.ge_u
 000829: 0b                         | end
00082b func[36] <i16x8_ge_s>:
 00082c: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 000835: e0 03 80 00 00 04 00 00 00 | 
 00083e: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000847: e0 03 00 00 00 04 81 00 00 | 
 000850: fd 35                      | i16x8.ge_s
 000852: 0b                         | end
000854 func[37] <i16x8_ge_u>:
 000855: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 00085e: e0 03 80 00 00 04 00 00 00 | 
 000867: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000870: e0 03 00 00 00 04 81 00 00 | 
 000879: fd 36                      | i16x8.ge_u
 00087b: 0b                         | end
00087d func[38] <i32x4_ge_s>:
 00087e: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 000887: e0 03 80 00 00 04 00 00 00 | 
 000890: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 000899: e0 03 00 00 00 04 81 00 00 | 
 0008a2: fd 3f                      | i32x4.ge_s
 0008a4: 0b                         | end
0008a6 func[39] <i32x4_ge_u>:
 0008a7: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00008003 0x00000004
 0008b0: e0 03 80 00 00 04 00 00 00 | 
 0008b9: fd 0c 01 00 00 02 02 ff 00 | v128.const 0x02000001 0xe000ff02 0x00000003 0x00008104
 0008c2: e0 03 00 00 00 04 81 00 00 | 
 0008cb: fd 40                      | i32x4.ge_u
 0008cd: 0b                         | end
0008cf func[40] <f32x4_ge_0>:
 0008d0: fd 0c 00 00 00 00 00 00 c0 | v128.const 0x00000000 0xffc00000 0x449a5000 0x449a5000
 0008d9: ff 00 50 9a 44 00 50 9a 44 | 
 0008e2: fd 0c 00 00 00 80 00 00 c0 | v128.const 0x80000000 0xffc00000 0xffc00000 0x3f800000
 0008eb: ff 00 00 c0 ff 00 00 80 3f | 
 0008f4: fd 46                      | f32x4.ge
 0008f6: 0b                         | end
0008f8 func[41] <f64x2_ge_0>:
 0008f9: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x00000000 0x00000000 0xfff80000
 000902: 00 00 00 00 00 00 00 f8 ff | 
 00090b: fd 0c 00 00 00 00 00 00 00 | v128.const 0x00000000 0x80000000 0x00000000 0xfff80000
 000914: 80 00 00 00 00 00 00 f8 ff | 
 00091d: fd 4c                      | f64x2.ge
 00091f: 0b                         | end
;;; STDOUT ;;)
