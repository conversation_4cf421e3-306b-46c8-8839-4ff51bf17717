;;; TOOL: run-objdump
;;; ARGS0: -v --enable-exceptions
(module
  (tag $e1 (param i32))
  (func
    try (result i32 i32)
      i32.const 1
      i32.const 2
    catch $e1
      drop
      i32.const 3
      i32.const 4
    end
    return)

  (func
    i32.const 0
    try (param i32)
      drop
    catch $e1
      drop
    end))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 03                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 01                                        ; num params
000000d: 7f                                        ; i32
000000e: 00                                        ; num results
; func type 1
000000f: 60                                        ; func
0000010: 00                                        ; num params
0000011: 00                                        ; num results
; func type 2
0000012: 60                                        ; func
0000013: 00                                        ; num params
0000014: 02                                        ; num results
0000015: 7f                                        ; i32
0000016: 7f                                        ; i32
0000009: 0d                                        ; FIXUP section size
; section "Function" (3)
0000017: 03                                        ; section code
0000018: 00                                        ; section size (guess)
0000019: 02                                        ; num functions
000001a: 01                                        ; function 0 signature index
000001b: 01                                        ; function 1 signature index
0000018: 03                                        ; FIXUP section size
; section "Tag" (13)
000001c: 0d                                        ; section code
000001d: 00                                        ; section size (guess)
000001e: 01                                        ; tag count
; tag 0
000001f: 00                                        ; tag attribute
0000020: 00                                        ; tag signature index
000001d: 03                                        ; FIXUP section size
; section "Code" (10)
0000021: 0a                                        ; section code
0000022: 00                                        ; section size (guess)
0000023: 02                                        ; num functions
; function body 0
0000024: 00                                        ; func body size (guess)
0000025: 00                                        ; local decl count
0000026: 06                                        ; try
0000027: 02                                        ; block type function index
0000028: 41                                        ; i32.const
0000029: 01                                        ; i32 literal
000002a: 41                                        ; i32.const
000002b: 02                                        ; i32 literal
000002c: 07                                        ; catch
000002d: 00                                        ; catch tag
000002e: 1a                                        ; drop
000002f: 41                                        ; i32.const
0000030: 03                                        ; i32 literal
0000031: 41                                        ; i32.const
0000032: 04                                        ; i32 literal
0000033: 0b                                        ; end
0000034: 0f                                        ; return
0000035: 0b                                        ; end
0000024: 11                                        ; FIXUP func body size
; function body 1
0000036: 00                                        ; func body size (guess)
0000037: 00                                        ; local decl count
0000038: 41                                        ; i32.const
0000039: 00                                        ; i32 literal
000003a: 06                                        ; try
000003b: 00                                        ; block type function index
000003c: 1a                                        ; drop
000003d: 07                                        ; catch
000003e: 00                                        ; catch tag
000003f: 1a                                        ; drop
0000040: 0b                                        ; end
0000041: 0b                                        ; end
0000036: 0b                                        ; FIXUP func body size
0000022: 1f                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

try-multi.wasm:	file format wasm 0x1

Code Disassembly:

000025 func[0]:
 000026: 06 02                      | try type[2]
 000028: 41 01                      |   i32.const 1
 00002a: 41 02                      |   i32.const 2
 00002c: 07 00                      | catch 0
 00002e: 1a                         |   drop
 00002f: 41 03                      |   i32.const 3
 000031: 41 04                      |   i32.const 4
 000033: 0b                         | end
 000034: 0f                         | return
 000035: 0b                         | end
000037 func[1]:
 000038: 41 00                      | i32.const 0
 00003a: 06 00                      | try type[0]
 00003c: 1a                         |   drop
 00003d: 07 00                      | catch 0
 00003f: 1a                         |   drop
 000040: 0b                         | end
 000041: 0b                         | end
;;; STDOUT ;;)
