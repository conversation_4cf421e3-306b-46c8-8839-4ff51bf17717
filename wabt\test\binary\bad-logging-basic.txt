;;; TOOL: run-gen-wasm-interp
;;; ARGS: -v
;;; ERROR: 1
magic
version
section(TYPE) { count[1] function params[0] results[0] }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[0]
  func {  ;; error
    return
  }
}
(;; STDERR ;;;
BeginModule(version: 1)
  BeginTypeSection(4)
    OnTypeCount(1)
    OnFuncType(index: 0, params: [], results: [])
  EndTypeSection
  BeginFunctionSection(2)
    OnFunctionCount(1)
    OnFunction(index: 0, sig_index: 0)
  EndFunctionSection
  BeginCodeSection(4)
0000015: error: function signature count != function body count
;;; STDERR ;;)
