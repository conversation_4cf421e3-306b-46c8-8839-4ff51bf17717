;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (memory
    (data "\00\01\02\03\04\05\06\07\08\09\0a")))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Memory" (5)
0000008: 05                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num memories
; memory 0
000000b: 01                                        ; limits: flags
000000c: 01                                        ; limits: initial
000000d: 01                                        ; limits: max
0000009: 04                                        ; FIXUP section size
; section "DataCount" (12)
000000e: 0c                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; data count
000000f: 01                                        ; FIXUP section size
; truncate to 14 (0xe)
; section "Data" (11)
000000e: 0b                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num data segments
; data segment header 0
0000011: 00                                        ; segment flags
0000012: 41                                        ; i32.const
0000013: 00                                        ; i32 literal
0000014: 0b                                        ; end
0000015: 0b                                        ; data segment size
; data segment data 0
0000016: 0001 0203 0405 0607 0809 0a               ; data segment data
000000f: 11                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

memory-hex.wasm:	file format wasm 0x1

Code Disassembly:

;;; STDOUT ;;)
