;;; TOOL: run-objdump
;;; ARGS0: -r --enable-all
;;; ARGS1: --headers --details
(module
  (type $t (func (param i32)))
  (import "__extern" "foo" (func (param i32) (result i32)))
  (import "__extern" "bar" (func (param i32) (param i32) (result i32)))
  (global $g i32 (i32.const 0))
  (func $f (param i32) (result i32)
    global.get 0
    call 2
    call 0
    i32.const 1234
    i32.const 0
    call_indirect (param i32) (param i32) (result i32))
  (export "f" (func $f))
  (table funcref (elem 1)))
(;; STDOUT ;;;

relocations-all-features.wasm:	file format wasm 0x1

Sections:

     Type start=0x0000000a end=0x0000001a (size=0x00000010) count: 3
   Import start=0x0000001c end=0x0000003b (size=0x0000001f) count: 2
 Function start=0x0000003d end=0x0000003f (size=0x00000002) count: 1
    Table start=0x00000041 end=0x00000046 (size=0x00000005) count: 1
   Global start=0x00000048 end=0x0000004e (size=0x00000006) count: 1
   Export start=0x00000050 end=0x00000055 (size=0x00000005) count: 1
     Elem start=0x00000057 end=0x0000005e (size=0x00000007) count: 1
     Code start=0x00000060 end=0x00000086 (size=0x00000026) count: 1
   Custom start=0x00000088 end=0x000000a9 (size=0x00000021) "linking"
   Custom start=0x000000ab end=0x000000c7 (size=0x0000001c) "reloc.Code"

Section Details:

Type[3]:
 - type[0] (i32) -> nil
 - type[1] (i32) -> i32
 - type[2] (i32, i32) -> i32
Import[2]:
 - func[0] sig=1 <__extern.foo> <- __extern.foo
 - func[1] sig=2 <__extern.bar> <- __extern.bar
Function[1]:
 - func[2] sig=1 <f>
Table[1]:
 - table[0] type=funcref initial=1 max=1
Global[1]:
 - global[0] i32 mutable=0 <g> - init i32=0
Export[1]:
 - func[2] <f> -> "f"
Elem[1]:
 - segment[0] flags=0 table=0 count=1 - init i32=0
  - elem[0] = func[1] <__extern.bar>
Code[1]:
 - func[2] size=36 <f>
Custom:
 - name: "linking"
  - symbol table [count=5]
   - 0: F <__extern.foo> func=0 [ undefined binding=global vis=default ]
   - 1: F <__extern.bar> func=1 [ undefined binding=global vis=default ]
   - 2: F <f> func=2 [ exported no_strip binding=global vis=hidden ]
   - 3: T <> table=0 [ binding=local vis=hidden ]
   - 4: G <g> global=0 [ binding=global vis=default ]
Custom:
 - name: "reloc.Code"
  - relocations for section: 7 (Code) [5]
   - R_WASM_GLOBAL_INDEX_LEB offset=0x000004(file=0x000064) symbol=4 <g>
   - R_WASM_FUNCTION_INDEX_LEB offset=0x00000a(file=0x00006a) symbol=2 <f>
   - R_WASM_FUNCTION_INDEX_LEB offset=0x000010(file=0x000070) symbol=0 <__extern.foo>
   - R_WASM_TYPE_INDEX_LEB offset=0x00001b(file=0x00007b) type=2
   - R_WASM_TABLE_NUMBER_LEB offset=0x000020(file=0x000080) symbol=3 <>

Code Disassembly:

000062 func[2] <f>:
 000063: 23 80 80 80 80 00          | global.get 0 <g>
           000064: R_WASM_GLOBAL_INDEX_LEB 4 <g>
 000069: 10 82 80 80 80 00          | call 2 <f>
           00006a: R_WASM_FUNCTION_INDEX_LEB 2 <f>
 00006f: 10 80 80 80 80 00          | call 0 <__extern.foo>
           000070: R_WASM_FUNCTION_INDEX_LEB 0 <__extern.foo>
 000075: 41 d2 09                   | i32.const 1234
 000078: 41 00                      | i32.const 0
 00007a: 11 82 80 80 80 00 80 80 80 | call_indirect 0 (type 2)
 000083: 80 00                      | 
           00007b: R_WASM_TYPE_INDEX_LEB 2
 000085: 0b                         | end
           000080: R_WASM_TABLE_NUMBER_LEB 3 <>
;;; STDOUT ;;)
