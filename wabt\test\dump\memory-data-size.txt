;;; TOOL: run-objdump-spec
;;; ARGS0: -v
;;; ARGS1: %(temp_file)s.0.wasm %(temp_file)s.1.wasm %(temp_file)s.2.wasm %(temp_file)s.3.wasm
(module (memory 1))
(module (memory 2))
(module (memory 4))
(module (memory 5))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Memory" (5)
0000008: 05                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num memories
; memory 0
000000b: 00                                        ; limits: flags
000000c: 01                                        ; limits: initial
0000009: 03                                        ; FIXUP section size
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Memory" (5)
0000008: 05                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num memories
; memory 0
000000b: 00                                        ; limits: flags
000000c: 02                                        ; limits: initial
0000009: 03                                        ; FIXUP section size
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Memory" (5)
0000008: 05                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num memories
; memory 0
000000b: 00                                        ; limits: flags
000000c: 04                                        ; limits: initial
0000009: 03                                        ; FIXUP section size
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Memory" (5)
0000008: 05                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num memories
; memory 0
000000b: 00                                        ; limits: flags
000000c: 05                                        ; limits: initial
0000009: 03                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

memory-data-size.0.wasm:	file format wasm 0x1

Code Disassembly:


memory-data-size.1.wasm:	file format wasm 0x1

Code Disassembly:


memory-data-size.2.wasm:	file format wasm 0x1

Code Disassembly:


memory-data-size.3.wasm:	file format wasm 0x1

Code Disassembly:

;;; STDOUT ;;)
