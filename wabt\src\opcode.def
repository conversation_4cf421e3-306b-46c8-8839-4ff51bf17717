/*
 * Copyright 2017 WebAssembly Community Group participants
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef WABT_OPCODE
#error "You must define WABT_OPCODE before including this file."
#endif

/* *** NOTE *** This list must be kept sorted so it can be binary searched */

/*
 *     tr: result type
 *     t1: type of the 1st parameter
 *     t2: type of the 2nd parameter
 *     t3: type of the 3rd parameter
 *      m: memory size of the operation, if any
 * prefix: the 1-byte opcode prefix, if any
 *   code: opcode
 *   Name: used to generate the opcode enum
 *   text: a string of the opcode name in the text format
 * decomp: an optional friendly version of text, used for decompilation.
 *
 *          tr    t1    t2    t3    m  prefix code  Name text
 * ==========================================================  */

WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x00, Unreachable, "unreachable", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x01, Nop, "nop", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x02, Block, "block", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x03, Loop, "loop", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x04, If, "if", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x05, Else, "else", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x06, Try, "try", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x07, Catch, "catch", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x08, Throw, "throw", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x09, Rethrow, "rethrow", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x0b, End, "end", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x0c, Br, "br", "")
WABT_OPCODE(___,  I32,  ___,  ___,  0,  0,    0x0d, BrIf, "br_if", "")
WABT_OPCODE(___,  I32,  ___,  ___,  0,  0,    0x0e, BrTable, "br_table", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x0f, Return, "return", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x10, Call, "call", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x11, CallIndirect, "call_indirect", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x12, ReturnCall, "return_call", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x13, ReturnCallIndirect, "return_call_indirect", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x14, CallRef, "call_ref", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x18, Delegate, "delegate", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x19, CatchAll, "catch_all", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x1a, Drop, "drop", "")
WABT_OPCODE(___,  ___,  ___,  I32,  0,  0,    0x1b, Select, "select", "")
WABT_OPCODE(___,  ___,  ___,  I32,  0,  0,    0x1c, SelectT, "select", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x20, LocalGet, "local.get", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x21, LocalSet, "local.set", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x22, LocalTee, "local.tee", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x23, GlobalGet, "global.get", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0x24, GlobalSet, "global.set", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  4,  0,    0x28, I32Load, "i32.load", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  8,  0,    0x29, I64Load, "i64.load", "")
WABT_OPCODE(F32,  I32,  ___,  ___,  4,  0,    0x2a, F32Load, "f32.load", "")
WABT_OPCODE(F64,  I32,  ___,  ___,  8,  0,    0x2b, F64Load, "f64.load", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  1,  0,    0x2c, I32Load8S, "i32.load8_s", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  1,  0,    0x2d, I32Load8U, "i32.load8_u", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  2,  0,    0x2e, I32Load16S, "i32.load16_s", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  2,  0,    0x2f, I32Load16U, "i32.load16_u", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  1,  0,    0x30, I64Load8S, "i64.load8_s", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  1,  0,    0x31, I64Load8U, "i64.load8_u", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  2,  0,    0x32, I64Load16S, "i64.load16_s", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  2,  0,    0x33, I64Load16U, "i64.load16_u", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  4,  0,    0x34, I64Load32S, "i64.load32_s", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  4,  0,    0x35, I64Load32U, "i64.load32_u", "")
WABT_OPCODE(___,  I32,  I32,  ___,  4,  0,    0x36, I32Store, "i32.store", "")
WABT_OPCODE(___,  I32,  I64,  ___,  8,  0,    0x37, I64Store, "i64.store", "")
WABT_OPCODE(___,  I32,  F32,  ___,  4,  0,    0x38, F32Store, "f32.store", "")
WABT_OPCODE(___,  I32,  F64,  ___,  8,  0,    0x39, F64Store, "f64.store", "")
WABT_OPCODE(___,  I32,  I32,  ___,  1,  0,    0x3a, I32Store8, "i32.store8", "")
WABT_OPCODE(___,  I32,  I32,  ___,  2,  0,    0x3b, I32Store16, "i32.store16", "")
WABT_OPCODE(___,  I32,  I64,  ___,  1,  0,    0x3c, I64Store8, "i64.store8", "")
WABT_OPCODE(___,  I32,  I64,  ___,  2,  0,    0x3d, I64Store16, "i64.store16", "")
WABT_OPCODE(___,  I32,  I64,  ___,  4,  0,    0x3e, I64Store32, "i64.store32", "")
WABT_OPCODE(I32,  ___,  ___,  ___,  0,  0,    0x3f, MemorySize, "memory.size", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  0,  0,    0x40, MemoryGrow, "memory.grow", "")
WABT_OPCODE(I32,  ___,  ___,  ___,  0,  0,    0x41, I32Const, "i32.const", "")
WABT_OPCODE(I64,  ___,  ___,  ___,  0,  0,    0x42, I64Const, "i64.const", "")
WABT_OPCODE(F32,  ___,  ___,  ___,  0,  0,    0x43, F32Const, "f32.const", "")
WABT_OPCODE(F64,  ___,  ___,  ___,  0,  0,    0x44, F64Const, "f64.const", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  0,  0,    0x45, I32Eqz, "i32.eqz", "eqz")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x46, I32Eq, "i32.eq", "==")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x47, I32Ne, "i32.ne", "!=")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x48, I32LtS, "i32.lt_s", "<")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x49, I32LtU, "i32.lt_u", "<")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x4a, I32GtS, "i32.gt_s", ">")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x4b, I32GtU, "i32.gt_u", ">")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x4c, I32LeS, "i32.le_s", "<=")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x4d, I32LeU, "i32.le_u", "<=")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x4e, I32GeS, "i32.ge_s", ">=")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x4f, I32GeU, "i32.ge_u", ">=")
WABT_OPCODE(I32,  I64,  ___,  ___,  0,  0,    0x50, I64Eqz, "i64.eqz", "eqz")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x51, I64Eq, "i64.eq", "==")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x52, I64Ne, "i64.ne", "!=")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x53, I64LtS, "i64.lt_s", "<")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x54, I64LtU, "i64.lt_u", "<")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x55, I64GtS, "i64.gt_s", ">")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x56, I64GtU, "i64.gt_u", ">")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x57, I64LeS, "i64.le_s", "<=")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x58, I64LeU, "i64.le_u", "<=")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x59, I64GeS, "i64.ge_s", ">=")
WABT_OPCODE(I32,  I64,  I64,  ___,  0,  0,    0x5a, I64GeU, "i64.ge_u", ">=")
WABT_OPCODE(I32,  F32,  F32,  ___,  0,  0,    0x5b, F32Eq, "f32.eq", "==")
WABT_OPCODE(I32,  F32,  F32,  ___,  0,  0,    0x5c, F32Ne, "f32.ne", "!=")
WABT_OPCODE(I32,  F32,  F32,  ___,  0,  0,    0x5d, F32Lt, "f32.lt", "<")
WABT_OPCODE(I32,  F32,  F32,  ___,  0,  0,    0x5e, F32Gt, "f32.gt", ">")
WABT_OPCODE(I32,  F32,  F32,  ___,  0,  0,    0x5f, F32Le, "f32.le", "<=")
WABT_OPCODE(I32,  F32,  F32,  ___,  0,  0,    0x60, F32Ge, "f32.ge", ">=")
WABT_OPCODE(I32,  F64,  F64,  ___,  0,  0,    0x61, F64Eq, "f64.eq", "==")
WABT_OPCODE(I32,  F64,  F64,  ___,  0,  0,    0x62, F64Ne, "f64.ne", "!=")
WABT_OPCODE(I32,  F64,  F64,  ___,  0,  0,    0x63, F64Lt, "f64.lt", "<")
WABT_OPCODE(I32,  F64,  F64,  ___,  0,  0,    0x64, F64Gt, "f64.gt", ">")
WABT_OPCODE(I32,  F64,  F64,  ___,  0,  0,    0x65, F64Le, "f64.le", "<=")
WABT_OPCODE(I32,  F64,  F64,  ___,  0,  0,    0x66, F64Ge, "f64.ge", ">=")
WABT_OPCODE(I32,  I32,  ___,  ___,  0,  0,    0x67, I32Clz, "i32.clz", "clz")
WABT_OPCODE(I32,  I32,  ___,  ___,  0,  0,    0x68, I32Ctz, "i32.ctz", "ctz")
WABT_OPCODE(I32,  I32,  ___,  ___,  0,  0,    0x69, I32Popcnt, "i32.popcnt", "popcnt")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x6a, I32Add, "i32.add", "+")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x6b, I32Sub, "i32.sub", "-")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x6c, I32Mul, "i32.mul", "*")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x6d, I32DivS, "i32.div_s", "/")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x6e, I32DivU, "i32.div_u", "/")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x6f, I32RemS, "i32.rem_s", "%")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x70, I32RemU, "i32.rem_u", "%")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x71, I32And, "i32.and", "&")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x72, I32Or, "i32.or", "|")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x73, I32Xor, "i32.xor", "^")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x74, I32Shl, "i32.shl", "<<")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x75, I32ShrS, "i32.shr_s", ">>")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x76, I32ShrU, "i32.shr_u", ">>")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x77, I32Rotl, "i32.rotl", "<<")
WABT_OPCODE(I32,  I32,  I32,  ___,  0,  0,    0x78, I32Rotr, "i32.rotr", ">>")
WABT_OPCODE(I64,  I64,  ___,  ___,  0,  0,    0x79, I64Clz, "i64.clz", "clz")
WABT_OPCODE(I64,  I64,  ___,  ___,  0,  0,    0x7a, I64Ctz, "i64.ctz", "ctz")
WABT_OPCODE(I64,  I64,  ___,  ___,  0,  0,    0x7b, I64Popcnt, "i64.popcnt", "popcnt")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x7c, I64Add, "i64.add", "+")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x7d, I64Sub, "i64.sub", "-")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x7e, I64Mul, "i64.mul", "*")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x7f, I64DivS, "i64.div_s", "/")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x80, I64DivU, "i64.div_u", "/")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x81, I64RemS, "i64.rem_s", "%")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x82, I64RemU, "i64.rem_u", "%")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x83, I64And, "i64.and", "&")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x84, I64Or, "i64.or", "|")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x85, I64Xor, "i64.xor", "^")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x86, I64Shl, "i64.shl", "<<")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x87, I64ShrS, "i64.shr_s", ">>")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x88, I64ShrU, "i64.shr_u", ">>")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x89, I64Rotl, "i64.rotl", "<<")
WABT_OPCODE(I64,  I64,  I64,  ___,  0,  0,    0x8a, I64Rotr, "i64.rotr", ">>")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x8b, F32Abs, "f32.abs", "abs")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x8c, F32Neg, "f32.neg", "-")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x8d, F32Ceil, "f32.ceil", "ceil")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x8e, F32Floor, "f32.floor", "floor")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x8f, F32Trunc, "f32.trunc", "trunc")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x90, F32Nearest, "f32.nearest", "nearest")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x91, F32Sqrt, "f32.sqrt", "sqrt")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x92, F32Add, "f32.add", "+")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x93, F32Sub, "f32.sub", "-")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x94, F32Mul, "f32.mul", "*")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x95, F32Div, "f32.div", "/")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x96, F32Min, "f32.min", "min")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x97, F32Max, "f32.max", "max")
WABT_OPCODE(F32,  F32,  F32,  ___,  0,  0,    0x98, F32Copysign, "f32.copysign", "copysign")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0x99, F64Abs, "f64.abs", "abs")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0x9a, F64Neg, "f64.neg", "-")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0x9b, F64Ceil, "f64.ceil", "ceil")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0x9c, F64Floor, "f64.floor", "floor")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0x9d, F64Trunc, "f64.trunc", "trunc")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0x9e, F64Nearest, "f64.nearest", "nearest")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0x9f, F64Sqrt, "f64.sqrt", "sqrt")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0xa0, F64Add, "f64.add", "+")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0xa1, F64Sub, "f64.sub", "-")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0xa2, F64Mul, "f64.mul", "*")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0xa3, F64Div, "f64.div", "/")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0xa4, F64Min, "f64.min", "min")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0xa5, F64Max, "f64.max", "max")
WABT_OPCODE(F64,  F64,  F64,  ___,  0,  0,    0xa6, F64Copysign, "f64.copysign", "copysign")
WABT_OPCODE(I32,  I64,  ___,  ___,  0,  0,    0xa7, I32WrapI64, "i32.wrap_i64", "")
WABT_OPCODE(I32,  F32,  ___,  ___,  0,  0,    0xa8, I32TruncF32S, "i32.trunc_f32_s", "")
WABT_OPCODE(I32,  F32,  ___,  ___,  0,  0,    0xa9, I32TruncF32U, "i32.trunc_f32_u", "")
WABT_OPCODE(I32,  F64,  ___,  ___,  0,  0,    0xaa, I32TruncF64S, "i32.trunc_f64_s", "")
WABT_OPCODE(I32,  F64,  ___,  ___,  0,  0,    0xab, I32TruncF64U, "i32.trunc_f64_u", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  0,  0,    0xac, I64ExtendI32S, "i64.extend_i32_s", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  0,  0,    0xad, I64ExtendI32U, "i64.extend_i32_u", "")
WABT_OPCODE(I64,  F32,  ___,  ___,  0,  0,    0xae, I64TruncF32S, "i64.trunc_f32_s", "")
WABT_OPCODE(I64,  F32,  ___,  ___,  0,  0,    0xaf, I64TruncF32U, "i64.trunc_f32_u", "")
WABT_OPCODE(I64,  F64,  ___,  ___,  0,  0,    0xb0, I64TruncF64S, "i64.trunc_f64_s", "")
WABT_OPCODE(I64,  F64,  ___,  ___,  0,  0,    0xb1, I64TruncF64U, "i64.trunc_f64_u", "")
WABT_OPCODE(F32,  I32,  ___,  ___,  0,  0,    0xb2, F32ConvertI32S, "f32.convert_i32_s", "")
WABT_OPCODE(F32,  I32,  ___,  ___,  0,  0,    0xb3, F32ConvertI32U, "f32.convert_i32_u", "")
WABT_OPCODE(F32,  I64,  ___,  ___,  0,  0,    0xb4, F32ConvertI64S, "f32.convert_i64_s", "")
WABT_OPCODE(F32,  I64,  ___,  ___,  0,  0,    0xb5, F32ConvertI64U, "f32.convert_i64_u", "")
WABT_OPCODE(F32,  F64,  ___,  ___,  0,  0,    0xb6, F32DemoteF64, "f32.demote_f64", "")
WABT_OPCODE(F64,  I32,  ___,  ___,  0,  0,    0xb7, F64ConvertI32S, "f64.convert_i32_s", "")
WABT_OPCODE(F64,  I32,  ___,  ___,  0,  0,    0xb8, F64ConvertI32U, "f64.convert_i32_u", "")
WABT_OPCODE(F64,  I64,  ___,  ___,  0,  0,    0xb9, F64ConvertI64S, "f64.convert_i64_s", "")
WABT_OPCODE(F64,  I64,  ___,  ___,  0,  0,    0xba, F64ConvertI64U, "f64.convert_i64_u", "")
WABT_OPCODE(F64,  F32,  ___,  ___,  0,  0,    0xbb, F64PromoteF32, "f64.promote_f32", "")
WABT_OPCODE(I32,  F32,  ___,  ___,  0,  0,    0xbc, I32ReinterpretF32, "i32.reinterpret_f32", "")
WABT_OPCODE(I64,  F64,  ___,  ___,  0,  0,    0xbd, I64ReinterpretF64, "i64.reinterpret_f64", "")
WABT_OPCODE(F32,  I32,  ___,  ___,  0,  0,    0xbe, F32ReinterpretI32, "f32.reinterpret_i32", "")
WABT_OPCODE(F64,  I64,  ___,  ___,  0,  0,    0xbf, F64ReinterpretI64, "f64.reinterpret_i64", "")

/* Sign-extension opcodes (--enable-sign-extension) */
WABT_OPCODE(I32,  I32,  ___,  ___,  0,  0,    0xC0, I32Extend8S, "i32.extend8_s", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  0,  0,    0xC1, I32Extend16S, "i32.extend16_s", "")
WABT_OPCODE(I64,  I64,  ___,  ___,  0,  0,    0xC2, I64Extend8S, "i64.extend8_s", "")
WABT_OPCODE(I64,  I64,  ___,  ___,  0,  0,    0xC3, I64Extend16S, "i64.extend16_s", "")
WABT_OPCODE(I64,  I64,  ___,  ___,  0,  0,    0xC4, I64Extend32S, "i64.extend32_s", "")

/* Interpreter-only opcodes */
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xe0, InterpAlloca, "alloca", "")
WABT_OPCODE(___,  I32,  ___,  ___,  0,  0,    0xe1, InterpBrUnless, "br_unless", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xe2, InterpCallImport, "call_import", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xe3, InterpData, "data", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xe4, InterpDropKeep, "drop_keep", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xe5, InterpCatchDrop, "catch_drop", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xe6, InterpAdjustFrameForReturnCall, "adjust_frame_for_return_call", "")

/* Saturating float-to-int opcodes (--enable-saturating-float-to-int) */
WABT_OPCODE(I32,  F32,  ___,  ___,  0,  0xfc, 0x00, I32TruncSatF32S, "i32.trunc_sat_f32_s", "")
WABT_OPCODE(I32,  F32,  ___,  ___,  0,  0xfc, 0x01, I32TruncSatF32U, "i32.trunc_sat_f32_u", "")
WABT_OPCODE(I32,  F64,  ___,  ___,  0,  0xfc, 0x02, I32TruncSatF64S, "i32.trunc_sat_f64_s", "")
WABT_OPCODE(I32,  F64,  ___,  ___,  0,  0xfc, 0x03, I32TruncSatF64U, "i32.trunc_sat_f64_u", "")
WABT_OPCODE(I64,  F32,  ___,  ___,  0,  0xfc, 0x04, I64TruncSatF32S, "i64.trunc_sat_f32_s", "")
WABT_OPCODE(I64,  F32,  ___,  ___,  0,  0xfc, 0x05, I64TruncSatF32U, "i64.trunc_sat_f32_u", "")
WABT_OPCODE(I64,  F64,  ___,  ___,  0,  0xfc, 0x06, I64TruncSatF64S, "i64.trunc_sat_f64_s", "")
WABT_OPCODE(I64,  F64,  ___,  ___,  0,  0xfc, 0x07, I64TruncSatF64U, "i64.trunc_sat_f64_u", "")

/* Bulk-memory */
WABT_OPCODE(___, I32,  I32,  I32,  0,  0xfc, 0x08, MemoryInit, "memory.init", "")
WABT_OPCODE(___, ___,  ___,  ___,  0,  0xfc, 0x09, DataDrop, "data.drop", "")
WABT_OPCODE(___, I32,  I32,  I32,  0,  0xfc, 0x0a, MemoryCopy,"memory.copy", "")
WABT_OPCODE(___, I32,  I32,  I32,  0,  0xfc, 0x0b, MemoryFill, "memory.fill", "")
WABT_OPCODE(___, I32,  I32,  I32,  0,  0xfc, 0x0c, TableInit, "table.init", "")
WABT_OPCODE(___, ___,  ___,  ___,  0,  0xfc, 0x0d, ElemDrop, "elem.drop", "")
WABT_OPCODE(___, I32,  I32,  I32,  0,  0xfc, 0x0e, TableCopy, "table.copy", "")

/* Reference types */
WABT_OPCODE(___,  I32,  ___,  ___,  0,  0,    0x25, TableGet, "table.get", "")
WABT_OPCODE(___,  I32,  ___,  ___,  0,  0,    0x26, TableSet, "table.set", "")
WABT_OPCODE(___,  ___,  I32,  ___,  0,  0xfc, 0x0f, TableGrow, "table.grow", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0xfc, 0x10, TableSize, "table.size", "")
WABT_OPCODE(___,  I32,  ___,  I32,  0,  0xfc, 0x11, TableFill, "table.fill", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xd0, RefNull, "ref.null", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xd1, RefIsNull, "ref.is_null", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0,    0xd2, RefFunc, "ref.func", "")

/* Simd opcodes */
WABT_OPCODE(V128, I32,  ___,  ___,  16, 0xfd, 0x00, V128Load,  "v128.load", "")
WABT_OPCODE(V128, I32,   ___, ___,  8,  0xfd, 0x01, V128Load8X8S, "v128.load8x8_s", "")
WABT_OPCODE(V128, I32,   ___, ___,  8,  0xfd, 0x02, V128Load8X8U, "v128.load8x8_u", "")
WABT_OPCODE(V128, I32,   ___, ___,  8,  0xfd, 0x03, V128Load16X4S, "v128.load16x4_s", "")
WABT_OPCODE(V128, I32,   ___, ___,  8,  0xfd, 0x04, V128Load16X4U, "v128.load16x4_u", "")
WABT_OPCODE(V128, I32,   ___, ___,  8,  0xfd, 0x05, V128Load32X2S, "v128.load32x2_s", "")
WABT_OPCODE(V128, I32,   ___, ___,  8,  0xfd, 0x06, V128Load32X2U, "v128.load32x2_u", "")
WABT_OPCODE(V128, I32,   ___, ___,  1,  0xfd, 0x07, V128Load8Splat, "v128.load8_splat", "")
WABT_OPCODE(V128, I32,   ___, ___,  2,  0xfd, 0x08, V128Load16Splat, "v128.load16_splat", "")
WABT_OPCODE(V128, I32,   ___, ___,  4,  0xfd, 0x09, V128Load32Splat, "v128.load32_splat", "")
WABT_OPCODE(V128, I32,   ___, ___,  8,  0xfd, 0x0a, V128Load64Splat, "v128.load64_splat", "")
WABT_OPCODE(___,  I32,  V128, ___,  16, 0xfd, 0x0b, V128Store, "v128.store", "")
WABT_OPCODE(V128, ___,  ___,  ___,  0,  0xfd, 0x0c, V128Const, "v128.const", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x0d, I8X16Shuffle, "i8x16.shuffle", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x0e, I8X16Swizzle, "i8x16.swizzle", "")
WABT_OPCODE(V128, I32,  ___,  ___,  0,  0xfd, 0x0f, I8X16Splat, "i8x16.splat", "")
WABT_OPCODE(V128, I32,  ___,  ___,  0,  0xfd, 0x10, I16X8Splat, "i16x8.splat", "")
WABT_OPCODE(V128, I32,  ___,  ___,  0,  0xfd, 0x11, I32X4Splat, "i32x4.splat", "")
WABT_OPCODE(V128, I64,  ___,  ___,  0,  0xfd, 0x12, I64X2Splat, "i64x2.splat", "")
WABT_OPCODE(V128, F32,  ___,  ___,  0,  0xfd, 0x13, F32X4Splat, "f32x4.splat", "")
WABT_OPCODE(V128, F64,  ___,  ___,  0,  0xfd, 0x14, F64X2Splat, "f64x2.splat", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x15, I8X16ExtractLaneS, "i8x16.extract_lane_s", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x16, I8X16ExtractLaneU, "i8x16.extract_lane_u", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x17, I8X16ReplaceLane, "i8x16.replace_lane", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x18, I16X8ExtractLaneS, "i16x8.extract_lane_s", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x19, I16X8ExtractLaneU, "i16x8.extract_lane_u", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x1a, I16X8ReplaceLane, "i16x8.replace_lane", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x1b, I32X4ExtractLane, "i32x4.extract_lane", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x1c, I32X4ReplaceLane, "i32x4.replace_lane", "")
WABT_OPCODE(I64,  V128, ___,  ___,  0,  0xfd, 0x1d, I64X2ExtractLane, "i64x2.extract_lane", "")
WABT_OPCODE(V128, V128, I64,  ___,  0,  0xfd, 0x1e, I64X2ReplaceLane, "i64x2.replace_lane", "")
WABT_OPCODE(F32,  V128, ___,  ___,  0,  0xfd, 0x1f, F32X4ExtractLane, "f32x4.extract_lane", "")
WABT_OPCODE(V128, V128, F32,  ___,  0,  0xfd, 0x20, F32X4ReplaceLane, "f32x4.replace_lane", "")
WABT_OPCODE(F64,  V128, ___,  ___,  0,  0xfd, 0x21, F64X2ExtractLane, "f64x2.extract_lane", "")
WABT_OPCODE(V128, V128, F64,  ___,  0,  0xfd, 0x22, F64X2ReplaceLane, "f64x2.replace_lane", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x23, I8X16Eq, "i8x16.eq", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x24, I8X16Ne, "i8x16.ne", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x25, I8X16LtS, "i8x16.lt_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x26, I8X16LtU, "i8x16.lt_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x27, I8X16GtS, "i8x16.gt_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x28, I8X16GtU, "i8x16.gt_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x29, I8X16LeS, "i8x16.le_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x2a, I8X16LeU, "i8x16.le_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x2b, I8X16GeS, "i8x16.ge_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x2c, I8X16GeU, "i8x16.ge_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x2d, I16X8Eq, "i16x8.eq", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x2e, I16X8Ne, "i16x8.ne", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x2f, I16X8LtS, "i16x8.lt_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x30, I16X8LtU, "i16x8.lt_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x31, I16X8GtS, "i16x8.gt_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x32, I16X8GtU, "i16x8.gt_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x33, I16X8LeS, "i16x8.le_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x34, I16X8LeU, "i16x8.le_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x35, I16X8GeS, "i16x8.ge_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x36, I16X8GeU, "i16x8.ge_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x37, I32X4Eq, "i32x4.eq", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x38, I32X4Ne, "i32x4.ne", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x39, I32X4LtS, "i32x4.lt_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x3a, I32X4LtU, "i32x4.lt_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x3b, I32X4GtS, "i32x4.gt_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x3c, I32X4GtU, "i32x4.gt_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x3d, I32X4LeS, "i32x4.le_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x3e, I32X4LeU, "i32x4.le_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x3f, I32X4GeS, "i32x4.ge_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x40, I32X4GeU, "i32x4.ge_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x41, F32X4Eq, "f32x4.eq", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x42, F32X4Ne, "f32x4.ne", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x43, F32X4Lt, "f32x4.lt", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x44, F32X4Gt, "f32x4.gt", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x45, F32X4Le, "f32x4.le", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x46, F32X4Ge, "f32x4.ge", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x47, F64X2Eq, "f64x2.eq", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x48, F64X2Ne, "f64x2.ne", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x49, F64X2Lt, "f64x2.lt", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x4a, F64X2Gt, "f64x2.gt", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x4b, F64X2Le, "f64x2.le", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x4c, F64X2Ge, "f64x2.ge", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x4d, V128Not, "v128.not", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x4e, V128And, "v128.and", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x4f, V128Andnot, "v128.andnot", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x50, V128Or,  "v128.or", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x51, V128Xor, "v128.xor", "")
WABT_OPCODE(V128, V128, V128, V128, 0,  0xfd, 0x52, V128BitSelect, "v128.bitselect", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x53, V128AnyTrue, "v128.any_true", "")
WABT_OPCODE(V128, I32, V128,  ___,  1,  0xfd, 0x54, V128Load8Lane, "v128.load8_lane", "")
WABT_OPCODE(V128, I32, V128,  ___,  2,  0xfd, 0x55, V128Load16Lane, "v128.load16_lane", "")
WABT_OPCODE(V128, I32, V128,  ___,  4,  0xfd, 0x56, V128Load32Lane, "v128.load32_lane", "")
WABT_OPCODE(V128, I32, V128,  ___,  8,  0xfd, 0x57, V128Load64Lane, "v128.load64_lane", "")
WABT_OPCODE(___,  I32, V128,  ___,  1,  0xfd, 0x58, V128Store8Lane, "v128.store8_lane", "")
WABT_OPCODE(___,  I32, V128,  ___,  2,  0xfd, 0x59, V128Store16Lane, "v128.store16_lane", "")
WABT_OPCODE(___,  I32, V128,  ___,  4,  0xfd, 0x5a, V128Store32Lane, "v128.store32_lane", "")
WABT_OPCODE(___,  I32, V128,  ___,  8,  0xfd, 0x5b, V128Store64Lane, "v128.store64_lane", "")
WABT_OPCODE(V128, I32,   ___, ___,  4,  0xfd, 0x5c, V128Load32Zero, "v128.load32_zero", "")
WABT_OPCODE(V128, I32,   ___, ___,  8,  0xfd, 0x5d, V128Load64Zero, "v128.load64_zero", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x5e, F32X4DemoteF64X2Zero, "f32x4.demote_f64x2_zero", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x5f, F64X2PromoteLowF32X4, "f64x2.promote_low_f32x4", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x60, I8X16Abs, "i8x16.abs", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x61, I8X16Neg, "i8x16.neg", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x62, I8X16Popcnt, "i8x16.popcnt", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x63, I8X16AllTrue, "i8x16.all_true", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x64, I8X16Bitmask, "i8x16.bitmask", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x65, I8X16NarrowI16X8S, "i8x16.narrow_i16x8_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x66, I8X16NarrowI16X8U, "i8x16.narrow_i16x8_u", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x6b, I8X16Shl, "i8x16.shl", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x6c, I8X16ShrS, "i8x16.shr_s", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x6d, I8X16ShrU, "i8x16.shr_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x6e, I8X16Add, "i8x16.add", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x6f, I8X16AddSatS, "i8x16.add_sat_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x70, I8X16AddSatU, "i8x16.add_sat_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x71, I8X16Sub, "i8x16.sub", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x72, I8X16SubSatS, "i8x16.sub_sat_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x73, I8X16SubSatU, "i8x16.sub_sat_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x76, I8X16MinS, "i8x16.min_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x77, I8X16MinU, "i8x16.min_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x78, I8X16MaxS, "i8x16.max_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x79, I8X16MaxU, "i8x16.max_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x7b, I8X16AvgrU, "i8x16.avgr_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x7c, I16X8ExtaddPairwiseI8X16S, "i16x8.extadd_pairwise_i8x16_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x7d, I16X8ExtaddPairwiseI8X16U, "i16x8.extadd_pairwise_i8x16_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x7e, I32X4ExtaddPairwiseI16X8S, "i32x4.extadd_pairwise_i16x8_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x7f, I32X4ExtaddPairwiseI16X8U, "i32x4.extadd_pairwise_i16x8_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x80, I16X8Abs, "i16x8.abs", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x81, I16X8Neg, "i16x8.neg", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x82, I16X8Q15mulrSatS, "i16x8.q15mulr_sat_s", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x83, I16X8AllTrue, "i16x8.all_true", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0x84, I16X8Bitmask, "i16x8.bitmask", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x85, I16X8NarrowI32X4S, "i16x8.narrow_i32x4_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x86, I16X8NarrowI32X4U, "i16x8.narrow_i32x4_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x87, I16X8ExtendLowI8X16S, "i16x8.extend_low_i8x16_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x88, I16X8ExtendHighI8X16S, "i16x8.extend_high_i8x16_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x89, I16X8ExtendLowI8X16U, "i16x8.extend_low_i8x16_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0x8a, I16X8ExtendHighI8X16U, "i16x8.extend_high_i8x16_u", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x8b, I16X8Shl, "i16x8.shl", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x8c, I16X8ShrS, "i16x8.shr_s", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0x8d, I16X8ShrU, "i16x8.shr_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x8e, I16X8Add, "i16x8.add", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x8f, I16X8AddSatS, "i16x8.add_sat_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x90, I16X8AddSatU, "i16x8.add_sat_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x91, I16X8Sub, "i16x8.sub", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x92, I16X8SubSatS, "i16x8.sub_sat_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x93, I16X8SubSatU, "i16x8.sub_sat_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x95, I16X8Mul, "i16x8.mul", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x96, I16X8MinS, "i16x8.min_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x97, I16X8MinU, "i16x8.min_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x98, I16X8MaxS, "i16x8.max_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x99, I16X8MaxU, "i16x8.max_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x9b, I16X8AvgrU, "i16x8.avgr_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x9c, I16X8ExtmulLowI8X16S, "i16x8.extmul_low_i8x16_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x9d, I16X8ExtmulHighI8X16S, "i16x8.extmul_high_i8x16_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x9e, I16X8ExtmulLowI8X16U, "i16x8.extmul_low_i8x16_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0x9f, I16X8ExtmulHighI8X16U, "i16x8.extmul_high_i8x16_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xa0, I32X4Abs, "i32x4.abs", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xa1, I32X4Neg, "i32x4.neg", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0xa3, I32X4AllTrue, "i32x4.all_true", "")
WABT_OPCODE(I32,  V128, ___,  ___,  0,  0xfd, 0xa4, I32X4Bitmask, "i32x4.bitmask", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xa7, I32X4ExtendLowI16X8S, "i32x4.extend_low_i16x8_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xa8, I32X4ExtendHighI16X8S, "i32x4.extend_high_i16x8_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xa9, I32X4ExtendLowI16X8U, "i32x4.extend_low_i16x8_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xaa, I32X4ExtendHighI16X8U, "i32x4.extend_high_i16x8_u", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0xab, I32X4Shl, "i32x4.shl", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0xac, I32X4ShrS, "i32x4.shr_s", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0xad, I32X4ShrU, "i32x4.shr_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xae, I32X4Add, "i32x4.add", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xb1, I32X4Sub, "i32x4.sub", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xb5, I32X4Mul, "i32x4.mul", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xb6, I32X4MinS, "i32x4.min_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xb7, I32X4MinU, "i32x4.min_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xb8, I32X4MaxS, "i32x4.max_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xb9, I32X4MaxU, "i32x4.max_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xba, I32X4DotI16X8S, "i32x4.dot_i16x8_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xbc, I32X4ExtmulLowI16X8S, "i32x4.extmul_low_i16x8_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xbd, I32X4ExtmulHighI16X8S, "i32x4.extmul_high_i16x8_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xbe, I32X4ExtmulLowI16X8U, "i32x4.extmul_low_i16x8_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xbf, I32X4ExtmulHighI16X8U, "i32x4.extmul_high_i16x8_u", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xc0, I64X2Abs, "i64x2.abs", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xc1, I64X2Neg, "i64x2.neg", "")
WABT_OPCODE( I32, V128,  ___, ___,  0,  0xfd, 0xc3, I64X2AllTrue, "i64x2.all_true", "")
WABT_OPCODE( I32, V128,  ___, ___,  0,  0xfd, 0xc4, I64X2Bitmask, "i64x2.bitmask", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xc7, I64X2ExtendLowI32X4S, "i64x2.extend_low_i32x4_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xc8, I64X2ExtendHighI32X4S, "i64x2.extend_high_i32x4_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xc9, I64X2ExtendLowI32X4U, "i64x2.extend_low_i32x4_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xca, I64X2ExtendHighI32X4U, "i64x2.extend_high_i32x4_u", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0xcb, I64X2Shl, "i64x2.shl", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0xcc, I64X2ShrS, "i64x2.shr_s", "")
WABT_OPCODE(V128, V128, I32,  ___,  0,  0xfd, 0xcd, I64X2ShrU, "i64x2.shr_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xce, I64X2Add, "i64x2.add", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xd1, I64X2Sub, "i64x2.sub", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xd5, I64X2Mul, "i64x2.mul", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xd6, I64X2Eq, "i64x2.eq", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xd7, I64X2Ne, "i64x2.ne", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xd8, I64X2LtS, "i64x2.lt_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xd9, I64X2GtS, "i64x2.gt_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xda, I64X2LeS, "i64x2.le_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xdb, I64X2GeS, "i64x2.ge_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xdc, I64X2ExtmulLowI32X4S, "i64x2.extmul_low_i32x4_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xdd, I64X2ExtmulHighI32X4S, "i64x2.extmul_high_i32x4_s", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xde, I64X2ExtmulLowI32X4U, "i64x2.extmul_low_i32x4_u", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xdf, I64X2ExtmulHighI32X4U, "i64x2.extmul_high_i32x4_u", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x67, F32X4Ceil, "f32x4.ceil", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x68, F32X4Floor, "f32x4.floor", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x69, F32X4Trunc, "f32x4.trunc", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x6a, F32X4Nearest, "f32x4.nearest", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x74, F64X2Ceil, "f64x2.ceil", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x75, F64X2Floor, "f64x2.floor", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x7a, F64X2Trunc, "f64x2.trunc", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0x94, F64X2Nearest, "f64x2.nearest", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xe0, F32X4Abs, "f32x4.abs", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xe1, F32X4Neg, "f32x4.neg", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xe3, F32X4Sqrt, "f32x4.sqrt", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xe4, F32X4Add, "f32x4.add", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xe5, F32X4Sub, "f32x4.sub", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xe6, F32X4Mul, "f32x4.mul", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xe7, F32X4Div, "f32x4.div", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xe8, F32X4Min, "f32x4.min", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xe9, F32X4Max, "f32x4.max", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xea, F32X4PMin, "f32x4.pmin", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xeb, F32X4PMax, "f32x4.pmax", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xec, F64X2Abs, "f64x2.abs", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xed, F64X2Neg, "f64x2.neg", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xef, F64X2Sqrt, "f64x2.sqrt", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xf0, F64X2Add, "f64x2.add", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xf1, F64X2Sub, "f64x2.sub", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xf2, F64X2Mul, "f64x2.mul", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xf3, F64X2Div, "f64x2.div", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xf4, F64X2Min, "f64x2.min", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xf5, F64X2Max, "f64x2.max", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xf6, F64X2PMin, "f64x2.pmin", "")
WABT_OPCODE(V128, V128, V128, ___,  0,  0xfd, 0xf7, F64X2PMax, "f64x2.pmax", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xf8, I32X4TruncSatF32X4S,"i32x4.trunc_sat_f32x4_s", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xf9, I32X4TruncSatF32X4U,"i32x4.trunc_sat_f32x4_u", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xfa, F32X4ConvertI32X4S, "f32x4.convert_i32x4_s", "")
WABT_OPCODE(V128, V128, ___,  ___,  0,  0xfd, 0xfb, F32X4ConvertI32X4U, "f32x4.convert_i32x4_u", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xfc, I32X4TruncSatF64X2SZero, "i32x4.trunc_sat_f64x2_s_zero", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xfd, I32X4TruncSatF64X2UZero, "i32x4.trunc_sat_f64x2_u_zero", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xfe, F64X2ConvertLowI32X4S, "f64x2.convert_low_i32x4_s", "")
WABT_OPCODE(V128, V128,  ___, ___,  0,  0xfd, 0xff, F64X2ConvertLowI32X4U, "f64x2.convert_low_i32x4_u", "")

/* Thread opcodes (--enable-threads) */
WABT_OPCODE(I32,  I32,  I32,  ___,  4,  0xfe, 0x00, MemoryAtomicNotify, "memory.atomic.notify", "")
WABT_OPCODE(I32,  I32,  I32,  I64,  4,  0xfe, 0x01, MemoryAtomicWait32, "memory.atomic.wait32", "")
WABT_OPCODE(I32,  I32,  I64,  I64,  8,  0xfe, 0x02, MemoryAtomicWait64, "memory.atomic.wait64", "")
WABT_OPCODE(___,  ___,  ___,  ___,  0,  0xfe, 0x03, AtomicFence, "atomic.fence", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  4,  0xfe, 0x10, I32AtomicLoad, "i32.atomic.load", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  8,  0xfe, 0x11, I64AtomicLoad, "i64.atomic.load", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  1,  0xfe, 0x12, I32AtomicLoad8U, "i32.atomic.load8_u", "")
WABT_OPCODE(I32,  I32,  ___,  ___,  2,  0xfe, 0x13, I32AtomicLoad16U, "i32.atomic.load16_u", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  1,  0xfe, 0x14, I64AtomicLoad8U, "i64.atomic.load8_u", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  2,  0xfe, 0x15, I64AtomicLoad16U, "i64.atomic.load16_u", "")
WABT_OPCODE(I64,  I32,  ___,  ___,  4,  0xfe, 0x16, I64AtomicLoad32U, "i64.atomic.load32_u", "")
WABT_OPCODE(___,  I32,  I32,  ___,  4,  0xfe, 0x17, I32AtomicStore, "i32.atomic.store", "")
WABT_OPCODE(___,  I32,  I64,  ___,  8,  0xfe, 0x18, I64AtomicStore, "i64.atomic.store", "")
WABT_OPCODE(___,  I32,  I32,  ___,  1,  0xfe, 0x19, I32AtomicStore8, "i32.atomic.store8", "")
WABT_OPCODE(___,  I32,  I32,  ___,  2,  0xfe, 0x1a, I32AtomicStore16, "i32.atomic.store16", "")
WABT_OPCODE(___,  I32,  I64,  ___,  1,  0xfe, 0x1b, I64AtomicStore8, "i64.atomic.store8", "")
WABT_OPCODE(___,  I32,  I64,  ___,  2,  0xfe, 0x1c, I64AtomicStore16, "i64.atomic.store16", "")
WABT_OPCODE(___,  I32,  I64,  ___,  4,  0xfe, 0x1d, I64AtomicStore32, "i64.atomic.store32", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  4,  0xfe, 0x1e, I32AtomicRmwAdd, "i32.atomic.rmw.add", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  8,  0xfe, 0x1f, I64AtomicRmwAdd, "i64.atomic.rmw.add", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  1,  0xfe, 0x20, I32AtomicRmw8AddU, "i32.atomic.rmw8.add_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  2,  0xfe, 0x21, I32AtomicRmw16AddU, "i32.atomic.rmw16.add_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  1,  0xfe, 0x22, I64AtomicRmw8AddU, "i64.atomic.rmw8.add_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  2,  0xfe, 0x23, I64AtomicRmw16AddU, "i64.atomic.rmw16.add_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  4,  0xfe, 0x24, I64AtomicRmw32AddU, "i64.atomic.rmw32.add_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  4,  0xfe, 0x25, I32AtomicRmwSub, "i32.atomic.rmw.sub", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  8,  0xfe, 0x26, I64AtomicRmwSub, "i64.atomic.rmw.sub", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  1,  0xfe, 0x27, I32AtomicRmw8SubU, "i32.atomic.rmw8.sub_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  2,  0xfe, 0x28, I32AtomicRmw16SubU, "i32.atomic.rmw16.sub_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  1,  0xfe, 0x29, I64AtomicRmw8SubU, "i64.atomic.rmw8.sub_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  2,  0xfe, 0x2a, I64AtomicRmw16SubU, "i64.atomic.rmw16.sub_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  4,  0xfe, 0x2b, I64AtomicRmw32SubU, "i64.atomic.rmw32.sub_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  4,  0xfe, 0x2c, I32AtomicRmwAnd, "i32.atomic.rmw.and", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  8,  0xfe, 0x2d, I64AtomicRmwAnd, "i64.atomic.rmw.and", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  1,  0xfe, 0x2e, I32AtomicRmw8AndU, "i32.atomic.rmw8.and_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  2,  0xfe, 0x2f, I32AtomicRmw16AndU, "i32.atomic.rmw16.and_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  1,  0xfe, 0x30, I64AtomicRmw8AndU, "i64.atomic.rmw8.and_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  2,  0xfe, 0x31, I64AtomicRmw16AndU, "i64.atomic.rmw16.and_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  4,  0xfe, 0x32, I64AtomicRmw32AndU, "i64.atomic.rmw32.and_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  4,  0xfe, 0x33, I32AtomicRmwOr, "i32.atomic.rmw.or", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  8,  0xfe, 0x34, I64AtomicRmwOr, "i64.atomic.rmw.or", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  1,  0xfe, 0x35, I32AtomicRmw8OrU, "i32.atomic.rmw8.or_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  2,  0xfe, 0x36, I32AtomicRmw16OrU, "i32.atomic.rmw16.or_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  1,  0xfe, 0x37, I64AtomicRmw8OrU, "i64.atomic.rmw8.or_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  2,  0xfe, 0x38, I64AtomicRmw16OrU, "i64.atomic.rmw16.or_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  4,  0xfe, 0x39, I64AtomicRmw32OrU, "i64.atomic.rmw32.or_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  4,  0xfe, 0x3a, I32AtomicRmwXor, "i32.atomic.rmw.xor", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  8,  0xfe, 0x3b, I64AtomicRmwXor, "i64.atomic.rmw.xor", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  1,  0xfe, 0x3c, I32AtomicRmw8XorU, "i32.atomic.rmw8.xor_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  2,  0xfe, 0x3d, I32AtomicRmw16XorU, "i32.atomic.rmw16.xor_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  1,  0xfe, 0x3e, I64AtomicRmw8XorU, "i64.atomic.rmw8.xor_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  2,  0xfe, 0x3f, I64AtomicRmw16XorU, "i64.atomic.rmw16.xor_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  4,  0xfe, 0x40, I64AtomicRmw32XorU, "i64.atomic.rmw32.xor_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  4,  0xfe, 0x41, I32AtomicRmwXchg, "i32.atomic.rmw.xchg", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  8,  0xfe, 0x42, I64AtomicRmwXchg, "i64.atomic.rmw.xchg", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  1,  0xfe, 0x43, I32AtomicRmw8XchgU, "i32.atomic.rmw8.xchg_u", "")
WABT_OPCODE(I32,  I32,  I32,  ___,  2,  0xfe, 0x44, I32AtomicRmw16XchgU, "i32.atomic.rmw16.xchg_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  1,  0xfe, 0x45, I64AtomicRmw8XchgU, "i64.atomic.rmw8.xchg_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  2,  0xfe, 0x46, I64AtomicRmw16XchgU, "i64.atomic.rmw16.xchg_u", "")
WABT_OPCODE(I64,  I32,  I64,  ___,  4,  0xfe, 0x47, I64AtomicRmw32XchgU, "i64.atomic.rmw32.xchg_u", "")
WABT_OPCODE(I32,  I32,  I32,  I32,  4,  0xfe, 0x48, I32AtomicRmwCmpxchg, "i32.atomic.rmw.cmpxchg", "")
WABT_OPCODE(I64,  I32,  I64,  I64,  8,  0xfe, 0x49, I64AtomicRmwCmpxchg, "i64.atomic.rmw.cmpxchg", "")
WABT_OPCODE(I32,  I32,  I32,  I32,  1,  0xfe, 0x4a, I32AtomicRmw8CmpxchgU, "i32.atomic.rmw8.cmpxchg_u", "")
WABT_OPCODE(I32,  I32,  I32,  I32,  2,  0xfe, 0x4b, I32AtomicRmw16CmpxchgU, "i32.atomic.rmw16.cmpxchg_u", "")
WABT_OPCODE(I64,  I32,  I64,  I64,  1,  0xfe, 0x4c, I64AtomicRmw8CmpxchgU, "i64.atomic.rmw8.cmpxchg_u", "")
WABT_OPCODE(I64,  I32,  I64,  I64,  2,  0xfe, 0x4d, I64AtomicRmw16CmpxchgU, "i64.atomic.rmw16.cmpxchg_u", "")
WABT_OPCODE(I64,  I32,  I64,  I64,  4,  0xfe, 0x4e, I64AtomicRmw32CmpxchgU, "i64.atomic.rmw32.cmpxchg_u", "")
