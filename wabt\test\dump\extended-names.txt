;;; TOOL: run-objdump
;;; ARGS0: -v --debug-names --enable-all
;;; ARGS1: -x
(type $type1 (func (param i32)))
(table $table1 1 funcref)
(table $table2 1 funcref)
(memory $mem2 1 1)
(data $data1 "hello")
(tag $mytag1 (param i64))
(tag $mytag2 (param i32))
(func
  i32.const 0
  throw $mytag2
  data.drop 0
)
(func
  i32.const 0
  i32.const 0
  call_indirect $table1 (type $type1)
)
;; A function with locals but no params
(func
  (local $local1 i32)
  (local $local2 i64)
  i32.const 0
  local.set 0
  local.get 0
  drop
  i64.const 0
  local.set 1
  local.get 1
  drop
)
;; A function with locals and params
(func
  (local $param1 i32)
  (local $param2 i64)
  (local $local1 i32)
  (local $local2 i64)
  i32.const 0
  local.set 0
  local.get 0
  drop
  i64.const 0
  local.set 1
  local.get 1
  drop
  i32.const 0
  local.set 2
  local.get 2
  drop
  i64.const 0
  local.set 3
  local.get 3
  drop
)

(elem $elem1 func 0)
(global $g1 (mut i32) (i32.const 1))
(global $g2 i32 (i32.const 2))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 03                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 01                                        ; num params
000000d: 7f                                        ; i32
000000e: 00                                        ; num results
; func type 1
000000f: 60                                        ; func
0000010: 01                                        ; num params
0000011: 7e                                        ; i64
0000012: 00                                        ; num results
; func type 2
0000013: 60                                        ; func
0000014: 00                                        ; num params
0000015: 00                                        ; num results
0000009: 0c                                        ; FIXUP section size
; section "Function" (3)
0000016: 03                                        ; section code
0000017: 00                                        ; section size (guess)
0000018: 04                                        ; num functions
0000019: 02                                        ; function 0 signature index
000001a: 02                                        ; function 1 signature index
000001b: 02                                        ; function 2 signature index
000001c: 02                                        ; function 3 signature index
0000017: 05                                        ; FIXUP section size
; section "Table" (4)
000001d: 04                                        ; section code
000001e: 00                                        ; section size (guess)
000001f: 02                                        ; num tables
; table 0
0000020: 70                                        ; funcref
0000021: 00                                        ; limits: flags
0000022: 01                                        ; limits: initial
; table 1
0000023: 70                                        ; funcref
0000024: 00                                        ; limits: flags
0000025: 01                                        ; limits: initial
000001e: 07                                        ; FIXUP section size
; section "Memory" (5)
0000026: 05                                        ; section code
0000027: 00                                        ; section size (guess)
0000028: 01                                        ; num memories
; memory 0
0000029: 01                                        ; limits: flags
000002a: 01                                        ; limits: initial
000002b: 01                                        ; limits: max
0000027: 04                                        ; FIXUP section size
; section "Tag" (13)
000002c: 0d                                        ; section code
000002d: 00                                        ; section size (guess)
000002e: 02                                        ; tag count
; tag 0
000002f: 00                                        ; tag attribute
0000030: 01                                        ; tag signature index
; tag 1
0000031: 00                                        ; tag attribute
0000032: 00                                        ; tag signature index
000002d: 05                                        ; FIXUP section size
; section "Global" (6)
0000033: 06                                        ; section code
0000034: 00                                        ; section size (guess)
0000035: 02                                        ; num globals
0000036: 7f                                        ; i32
0000037: 01                                        ; global mutability
0000038: 41                                        ; i32.const
0000039: 01                                        ; i32 literal
000003a: 0b                                        ; end
000003b: 7f                                        ; i32
000003c: 00                                        ; global mutability
000003d: 41                                        ; i32.const
000003e: 02                                        ; i32 literal
000003f: 0b                                        ; end
0000034: 0b                                        ; FIXUP section size
; section "Elem" (9)
0000040: 09                                        ; section code
0000041: 00                                        ; section size (guess)
0000042: 01                                        ; num elem segments
; elem segment header 0
0000043: 01                                        ; segment flags
0000044: 00                                        ; elem list type
0000045: 01                                        ; num elems
0000046: 00                                        ; elem function index
0000041: 05                                        ; FIXUP section size
; section "DataCount" (12)
0000047: 0c                                        ; section code
0000048: 00                                        ; section size (guess)
0000049: 01                                        ; data count
0000048: 01                                        ; FIXUP section size
; section "Code" (10)
000004a: 0a                                        ; section code
000004b: 00                                        ; section size (guess)
000004c: 04                                        ; num functions
; function body 0
000004d: 00                                        ; func body size (guess)
000004e: 00                                        ; local decl count
000004f: 41                                        ; i32.const
0000050: 00                                        ; i32 literal
0000051: 08                                        ; throw
0000052: 01                                        ; throw tag
0000053: fc                                        ; prefix
0000054: 09                                        ; data.drop
0000055: 00                                        ; data.drop segment
0000056: 0b                                        ; end
000004d: 09                                        ; FIXUP func body size
; function body 1
0000057: 00                                        ; func body size (guess)
0000058: 00                                        ; local decl count
0000059: 41                                        ; i32.const
000005a: 00                                        ; i32 literal
000005b: 41                                        ; i32.const
000005c: 00                                        ; i32 literal
000005d: 11                                        ; call_indirect
000005e: 00                                        ; signature index
000005f: 00                                        ; table index
0000060: 0b                                        ; end
0000057: 09                                        ; FIXUP func body size
; function body 2
0000061: 00                                        ; func body size (guess)
0000062: 02                                        ; local decl count
0000063: 01                                        ; local type count
0000064: 7f                                        ; i32
0000065: 01                                        ; local type count
0000066: 7e                                        ; i64
0000067: 41                                        ; i32.const
0000068: 00                                        ; i32 literal
0000069: 21                                        ; local.set
000006a: 00                                        ; local index
000006b: 20                                        ; local.get
000006c: 00                                        ; local index
000006d: 1a                                        ; drop
000006e: 42                                        ; i64.const
000006f: 00                                        ; i64 literal
0000070: 21                                        ; local.set
0000071: 01                                        ; local index
0000072: 20                                        ; local.get
0000073: 01                                        ; local index
0000074: 1a                                        ; drop
0000075: 0b                                        ; end
0000061: 14                                        ; FIXUP func body size
; function body 3
0000076: 00                                        ; func body size (guess)
0000077: 04                                        ; local decl count
0000078: 01                                        ; local type count
0000079: 7f                                        ; i32
000007a: 01                                        ; local type count
000007b: 7e                                        ; i64
000007c: 01                                        ; local type count
000007d: 7f                                        ; i32
000007e: 01                                        ; local type count
000007f: 7e                                        ; i64
0000080: 41                                        ; i32.const
0000081: 00                                        ; i32 literal
0000082: 21                                        ; local.set
0000083: 00                                        ; local index
0000084: 20                                        ; local.get
0000085: 00                                        ; local index
0000086: 1a                                        ; drop
0000087: 42                                        ; i64.const
0000088: 00                                        ; i64 literal
0000089: 21                                        ; local.set
000008a: 01                                        ; local index
000008b: 20                                        ; local.get
000008c: 01                                        ; local index
000008d: 1a                                        ; drop
000008e: 41                                        ; i32.const
000008f: 00                                        ; i32 literal
0000090: 21                                        ; local.set
0000091: 02                                        ; local index
0000092: 20                                        ; local.get
0000093: 02                                        ; local index
0000094: 1a                                        ; drop
0000095: 42                                        ; i64.const
0000096: 00                                        ; i64 literal
0000097: 21                                        ; local.set
0000098: 03                                        ; local index
0000099: 20                                        ; local.get
000009a: 03                                        ; local index
000009b: 1a                                        ; drop
000009c: 0b                                        ; end
0000076: 26                                        ; FIXUP func body size
000004b: 51                                        ; FIXUP section size
; section "Data" (11)
000009d: 0b                                        ; section code
000009e: 00                                        ; section size (guess)
000009f: 01                                        ; num data segments
; data segment header 0
00000a0: 01                                        ; segment flags
00000a1: 05                                        ; data segment size
; data segment data 0
00000a2: 6865 6c6c 6f                              ; data segment data
000009e: 08                                        ; FIXUP section size
; section "name"
00000a7: 00                                        ; section code
00000a8: 00                                        ; section size (guess)
00000a9: 04                                        ; string length
00000aa: 6e61 6d65                                name  ; custom section name
00000ae: 02                                        ; local name type
00000af: 00                                        ; subsection size (guess)
00000b0: 04                                        ; num functions
00000b1: 00                                        ; function index
00000b2: 00                                        ; num locals
00000b3: 01                                        ; function index
00000b4: 00                                        ; num locals
00000b5: 02                                        ; function index
00000b6: 02                                        ; num locals
00000b7: 00                                        ; local index
00000b8: 06                                        ; string length
00000b9: 6c6f 6361 6c31                           local1  ; local name 0
00000bf: 01                                        ; local index
00000c0: 06                                        ; string length
00000c1: 6c6f 6361 6c32                           local2  ; local name 1
00000c7: 03                                        ; function index
00000c8: 04                                        ; num locals
00000c9: 00                                        ; local index
00000ca: 06                                        ; string length
00000cb: 7061 7261 6d31                           param1  ; local name 0
00000d1: 01                                        ; local index
00000d2: 06                                        ; string length
00000d3: 7061 7261 6d32                           param2  ; local name 1
00000d9: 02                                        ; local index
00000da: 06                                        ; string length
00000db: 6c6f 6361 6c31                           local1  ; local name 2
00000e1: 03                                        ; local index
00000e2: 06                                        ; string length
00000e3: 6c6f 6361 6c32                           local2  ; local name 3
00000af: 39                                        ; FIXUP subsection size
00000e9: 04                                        ; name subsection type
00000ea: 00                                        ; subsection size (guess)
00000eb: 01                                        ; num names
00000ec: 00                                        ; elem index
00000ed: 05                                        ; string length
00000ee: 7479 7065 31                             type1  ; elem name 0
00000ea: 08                                        ; FIXUP subsection size
00000f3: 05                                        ; name subsection type
00000f4: 00                                        ; subsection size (guess)
00000f5: 02                                        ; num names
00000f6: 00                                        ; elem index
00000f7: 06                                        ; string length
00000f8: 7461 626c 6531                           table1  ; elem name 0
00000fe: 01                                        ; elem index
00000ff: 06                                        ; string length
0000100: 7461 626c 6532                           table2  ; elem name 1
00000f4: 11                                        ; FIXUP subsection size
0000106: 06                                        ; name subsection type
0000107: 00                                        ; subsection size (guess)
0000108: 01                                        ; num names
0000109: 00                                        ; elem index
000010a: 04                                        ; string length
000010b: 6d65 6d32                                mem2  ; elem name 0
0000107: 07                                        ; FIXUP subsection size
000010f: 07                                        ; name subsection type
0000110: 00                                        ; subsection size (guess)
0000111: 02                                        ; num names
0000112: 00                                        ; elem index
0000113: 02                                        ; string length
0000114: 6731                                     g1  ; elem name 0
0000116: 01                                        ; elem index
0000117: 02                                        ; string length
0000118: 6732                                     g2  ; elem name 1
0000110: 09                                        ; FIXUP subsection size
000011a: 08                                        ; name subsection type
000011b: 00                                        ; subsection size (guess)
000011c: 01                                        ; num names
000011d: 00                                        ; elem index
000011e: 05                                        ; string length
000011f: 656c 656d 31                             elem1  ; elem name 0
000011b: 08                                        ; FIXUP subsection size
0000124: 09                                        ; name subsection type
0000125: 00                                        ; subsection size (guess)
0000126: 01                                        ; num names
0000127: 00                                        ; elem index
0000128: 05                                        ; string length
0000129: 6461 7461 31                             data1  ; elem name 0
0000125: 08                                        ; FIXUP subsection size
000012e: 0a                                        ; name subsection type
000012f: 00                                        ; subsection size (guess)
0000130: 02                                        ; num names
0000131: 00                                        ; elem index
0000132: 06                                        ; string length
0000133: 6d79 7461 6731                           mytag1  ; elem name 0
0000139: 01                                        ; elem index
000013a: 06                                        ; string length
000013b: 6d79 7461 6732                           mytag2  ; elem name 1
000012f: 11                                        ; FIXUP subsection size
; move data: [a9, 141) -> [aa, 142)
00000a8: 9801                                      ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

extended-names.wasm:	file format wasm 0x1

Section Details:

Type[3]:
 - type[0] (i32) -> nil
 - type[1] (i64) -> nil
 - type[2] () -> nil
Function[4]:
 - func[0] sig=2
 - func[1] sig=2
 - func[2] sig=2
 - func[3] sig=2
Table[2]:
 - table[0] type=funcref initial=1 <table1>
 - table[1] type=funcref initial=1 <table2>
Memory[1]:
 - memory[0] pages: initial=1 max=1
Tag[2]:
 - tag[0] sig=1
 - tag[1] sig=0
Global[2]:
 - global[0] i32 mutable=1 <g1> - init i32=1
 - global[1] i32 mutable=0 <g2> - init i32=2
Elem[1]:
 - segment[0] flags=1 table=0 count=1
  - elem[0] = func[0]
DataCount:
 - data count: 1
Code[4]:
 - func[0] size=9
 - func[1] size=9
 - func[2] size=20
 - func[3] size=38
Data[1]:
 - segment[0] <data1> passive size=5
  - 0000000: 6865 6c6c 6f                             hello
Custom:
 - name: "name"
 - func[2] local[0] <local1>
 - func[2] local[1] <local2>
 - func[3] local[0] <param1>
 - func[3] local[1] <param2>
 - func[3] local[2] <local1>
 - func[3] local[3] <local2>
 - type[0] <type1>
 - table[0] <table1>
 - table[1] <table2>
 - memory[0] <mem2>
 - global[0] <g1>
 - global[1] <g2>
 - elemseg[0] <elem1>
 - dataseg[0] <data1>
 - tag[0] <mytag1>
 - tag[1] <mytag2>

Code Disassembly:

00004e func[0]:
 00004f: 41 00                      | i32.const 0
 000051: 08 01                      | throw 1 <mytag2>
 000053: fc 09 00                   | data.drop 0 <data1>
 000056: 0b                         | end
000058 func[1]:
 000059: 41 00                      | i32.const 0
 00005b: 41 00                      | i32.const 0
 00005d: 11 00 00                   | call_indirect 0 <table1> (type 0 <type1>)
 000060: 0b                         | end
000062 func[2]:
 000063: 01 7f                      | local[0] type=i32
 000065: 01 7e                      | local[1] type=i64
 000067: 41 00                      | i32.const 0
 000069: 21 00                      | local.set 0 <local1>
 00006b: 20 00                      | local.get 0 <local1>
 00006d: 1a                         | drop
 00006e: 42 00                      | i64.const 0
 000070: 21 01                      | local.set 1 <local2>
 000072: 20 01                      | local.get 1 <local2>
 000074: 1a                         | drop
 000075: 0b                         | end
000077 func[3]:
 000078: 01 7f                      | local[0] type=i32
 00007a: 01 7e                      | local[1] type=i64
 00007c: 01 7f                      | local[2] type=i32
 00007e: 01 7e                      | local[3] type=i64
 000080: 41 00                      | i32.const 0
 000082: 21 00                      | local.set 0 <param1>
 000084: 20 00                      | local.get 0 <param1>
 000086: 1a                         | drop
 000087: 42 00                      | i64.const 0
 000089: 21 01                      | local.set 1 <param2>
 00008b: 20 01                      | local.get 1 <param2>
 00008d: 1a                         | drop
 00008e: 41 00                      | i32.const 0
 000090: 21 02                      | local.set 2 <local1>
 000092: 20 02                      | local.get 2 <local1>
 000094: 1a                         | drop
 000095: 42 00                      | i64.const 0
 000097: 21 03                      | local.set 3 <local2>
 000099: 20 03                      | local.get 3 <local2>
 00009b: 1a                         | drop
 00009c: 0b                         | end
;;; STDOUT ;;)
