;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    f32.const 0x0p0
    drop
    f32.const 0x1234.5p6
    drop
    f32.const 0xffffffffp20
    drop
    f32.const 0x1p127
    drop
    f32.const 0x0.08p127
    drop
    f32.const 0x2.46p+123
    drop
    f32.const 0x0.fffffp127
    drop
    f32.const 0x0.7fffffp127
    drop
    f32.const 0x0.ffffffffp127
    drop
    f32.const 0x1.ffff88p127
    drop
    f32.const 0x1.fffff1p127
    drop
    f32.const 0xfffff98p-133
    drop
    f32.const 0xfffff88p-133
    drop
    f32.const 0xfffffffffp-155 
    drop
    f32.const 0x000000001.10000000000p0
    drop
    f32.const 0x1000000000.p4
    drop
    f32.const -0x1.ff01p1
    drop
  )
)
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 00                                        ; local decl count
0000017: 43                                        ; f32.const
0000018: 0000 0000                                 ; f32 literal
000001c: 1a                                        ; drop
000001d: 43                                        ; f32.const
000001e: 80a2 9148                                 ; f32 literal
0000022: 1a                                        ; drop
0000023: 43                                        ; f32.const
0000024: 0000 8059                                 ; f32 literal
0000028: 1a                                        ; drop
0000029: 43                                        ; f32.const
000002a: 0000 007f                                 ; f32 literal
000002e: 1a                                        ; drop
000002f: 43                                        ; f32.const
0000030: 0000 807c                                 ; f32 literal
0000034: 1a                                        ; drop
0000035: 43                                        ; f32.const
0000036: 0080 917d                                 ; f32 literal
000003a: 1a                                        ; drop
000003b: 43                                        ; f32.const
000003c: f0ff ff7e                                 ; f32 literal
0000040: 1a                                        ; drop
0000041: 43                                        ; f32.const
0000042: feff 7f7e                                 ; f32 literal
0000046: 1a                                        ; drop
0000047: 43                                        ; f32.const
0000048: 0000 007f                                 ; f32 literal
000004c: 1a                                        ; drop
000004d: 43                                        ; f32.const
000004e: c4ff 7f7f                                 ; f32 literal
0000052: 1a                                        ; drop
0000053: 43                                        ; f32.const
0000054: f8ff 7f7f                                 ; f32 literal
0000058: 1a                                        ; drop
0000059: 43                                        ; f32.const
000005a: faff ff0a                                 ; f32 literal
000005e: 1a                                        ; drop
000005f: 43                                        ; f32.const
0000060: f8ff ff0a                                 ; f32 literal
0000064: 1a                                        ; drop
0000065: 43                                        ; f32.const
0000066: 0000 0004                                 ; f32 literal
000006a: 1a                                        ; drop
000006b: 43                                        ; f32.const
000006c: 0000 883f                                 ; f32 literal
0000070: 1a                                        ; drop
0000071: 43                                        ; f32.const
0000072: 0000 8053                                 ; f32 literal
0000076: 1a                                        ; drop
0000077: 43                                        ; f32.const
0000078: 8080 7fc0                                 ; f32 literal
000007c: 1a                                        ; drop
000007d: 0b                                        ; end
0000015: 68                                        ; FIXUP func body size
0000013: 6a                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

hexfloat_f32.wasm:	file format wasm 0x1

Code Disassembly:

000016 func[0]:
 000017: 43 00 00 00 00             | f32.const 0x0p+0
 00001c: 1a                         | drop
 00001d: 43 80 a2 91 48             | f32.const 0x1.2345p+18
 000022: 1a                         | drop
 000023: 43 00 00 80 59             | f32.const 0x1p+52
 000028: 1a                         | drop
 000029: 43 00 00 00 7f             | f32.const 0x1p+127
 00002e: 1a                         | drop
 00002f: 43 00 00 80 7c             | f32.const 0x1p+122
 000034: 1a                         | drop
 000035: 43 00 80 91 7d             | f32.const 0x1.23p+124
 00003a: 1a                         | drop
 00003b: 43 f0 ff ff 7e             | f32.const 0x1.ffffep+126
 000040: 1a                         | drop
 000041: 43 fe ff 7f 7e             | f32.const 0x1.fffffcp+125
 000046: 1a                         | drop
 000047: 43 00 00 00 7f             | f32.const 0x1p+127
 00004c: 1a                         | drop
 00004d: 43 c4 ff 7f 7f             | f32.const 0x1.ffff88p+127
 000052: 1a                         | drop
 000053: 43 f8 ff 7f 7f             | f32.const 0x1.fffffp+127
 000058: 1a                         | drop
 000059: 43 fa ff ff 0a             | f32.const 0x1.fffff4p-106
 00005e: 1a                         | drop
 00005f: 43 f8 ff ff 0a             | f32.const 0x1.fffffp-106
 000064: 1a                         | drop
 000065: 43 00 00 00 04             | f32.const 0x1p-119
 00006a: 1a                         | drop
 00006b: 43 00 00 88 3f             | f32.const 0x1.1p+0
 000070: 1a                         | drop
 000071: 43 00 00 80 53             | f32.const 0x1p+40
 000076: 1a                         | drop
 000077: 43 80 80 7f c0             | f32.const -0x1.ff01p+1
 00007c: 1a                         | drop
 00007d: 0b                         | end
;;; STDOUT ;;)
