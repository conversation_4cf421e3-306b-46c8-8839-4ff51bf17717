;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (memory 1)
  (func (result i32)
    memory.size))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 01                                        ; num results
000000e: 7f                                        ; i32
0000009: 05                                        ; FIXUP section size
; section "Function" (3)
000000f: 03                                        ; section code
0000010: 00                                        ; section size (guess)
0000011: 01                                        ; num functions
0000012: 00                                        ; function 0 signature index
0000010: 02                                        ; FIXUP section size
; section "Memory" (5)
0000013: 05                                        ; section code
0000014: 00                                        ; section size (guess)
0000015: 01                                        ; num memories
; memory 0
0000016: 00                                        ; limits: flags
0000017: 01                                        ; limits: initial
0000014: 03                                        ; FIXUP section size
; section "Code" (10)
0000018: 0a                                        ; section code
0000019: 00                                        ; section size (guess)
000001a: 01                                        ; num functions
; function body 0
000001b: 00                                        ; func body size (guess)
000001c: 00                                        ; local decl count
000001d: 3f                                        ; memory.size
000001e: 00                                        ; memory.size memidx
000001f: 0b                                        ; end
000001b: 04                                        ; FIXUP func body size
0000019: 06                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

memory-size.wasm:	file format wasm 0x1

Code Disassembly:

00001c func[0]:
 00001d: 3f 00                      | memory.size 0
 00001f: 0b                         | end
;;; STDOUT ;;)
