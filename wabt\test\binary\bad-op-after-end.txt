;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func {
    locals[0]
    end
    nop
  }
}
(;; STDERR ;;;
error: accessing stack depth: 0 >= max: 0
000001a: error: OnNopExpr callback failed
error: accessing stack depth: 0 >= max: 0
000001a: error: OnNopExpr callback failed
;;; STDERR ;;)
