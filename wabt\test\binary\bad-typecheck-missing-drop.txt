;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[0] }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func {
    locals[0]
    i32.const 1
    i32.const 2
    i32.add
  }
}
(;; STDERR ;;;
out/test/binary/bad-typecheck-missing-drop/bad-typecheck-missing-drop.wasm:000001c: error: type mismatch at end of function, expected [] but got [i32]
out/test/binary/bad-typecheck-missing-drop/bad-typecheck-missing-drop.wasm:000001c: error: type mismatch at end of function, expected [] but got [i32]
;;; STDERR ;;)
