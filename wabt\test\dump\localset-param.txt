;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  ;;             0   1
  (func (param i32 f32)
    ;; i32           2
    ;; i64   3
    ;; f32       4       5
    (local i64 f32 i32 f32)
    i32.const 0
    local.set 0
    f32.const 0
    local.set 1
    i64.const 0
    local.set 2
    f32.const 0
    local.set 3
    i32.const 0
    local.set 4
    f32.const 0
    local.set 5))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 02                                        ; num params
000000d: 7f                                        ; i32
000000e: 7d                                        ; f32
000000f: 00                                        ; num results
0000009: 06                                        ; FIXUP section size
; section "Function" (3)
0000010: 03                                        ; section code
0000011: 00                                        ; section size (guess)
0000012: 01                                        ; num functions
0000013: 00                                        ; function 0 signature index
0000011: 02                                        ; FIXUP section size
; section "Code" (10)
0000014: 0a                                        ; section code
0000015: 00                                        ; section size (guess)
0000016: 01                                        ; num functions
; function body 0
0000017: 00                                        ; func body size (guess)
0000018: 04                                        ; local decl count
0000019: 01                                        ; local type count
000001a: 7e                                        ; i64
000001b: 01                                        ; local type count
000001c: 7d                                        ; f32
000001d: 01                                        ; local type count
000001e: 7f                                        ; i32
000001f: 01                                        ; local type count
0000020: 7d                                        ; f32
0000021: 41                                        ; i32.const
0000022: 00                                        ; i32 literal
0000023: 21                                        ; local.set
0000024: 00                                        ; local index
0000025: 43                                        ; f32.const
0000026: 0000 0000                                 ; f32 literal
000002a: 21                                        ; local.set
000002b: 01                                        ; local index
000002c: 42                                        ; i64.const
000002d: 00                                        ; i64 literal
000002e: 21                                        ; local.set
000002f: 02                                        ; local index
0000030: 43                                        ; f32.const
0000031: 0000 0000                                 ; f32 literal
0000035: 21                                        ; local.set
0000036: 03                                        ; local index
0000037: 41                                        ; i32.const
0000038: 00                                        ; i32 literal
0000039: 21                                        ; local.set
000003a: 04                                        ; local index
000003b: 43                                        ; f32.const
000003c: 0000 0000                                 ; f32 literal
0000040: 21                                        ; local.set
0000041: 05                                        ; local index
0000042: 0b                                        ; end
0000017: 2b                                        ; FIXUP func body size
0000015: 2d                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

localset-param.wasm:	file format wasm 0x1

Code Disassembly:

000018 func[0]:
 000019: 01 7e                      | local[2] type=i64
 00001b: 01 7d                      | local[3] type=f32
 00001d: 01 7f                      | local[4] type=i32
 00001f: 01 7d                      | local[5] type=f32
 000021: 41 00                      | i32.const 0
 000023: 21 00                      | local.set 0
 000025: 43 00 00 00 00             | f32.const 0x0p+0
 00002a: 21 01                      | local.set 1
 00002c: 42 00                      | i64.const 0
 00002e: 21 02                      | local.set 2
 000030: 43 00 00 00 00             | f32.const 0x0p+0
 000035: 21 03                      | local.set 3
 000037: 41 00                      | i32.const 0
 000039: 21 04                      | local.set 4
 00003b: 43 00 00 00 00             | f32.const 0x0p+0
 000040: 21 05                      | local.set 5
 000042: 0b                         | end
;;; STDOUT ;;)
