html {
  font-size: 80%;
  line-height: 1.3;
}

* {
  box-sizing: border-box;
}

body {
  font-family: sans-serif;
  font-size: 1rem;
  line-height: 1.3125rem;
  margin: 0;
}

h1 {
  font-size: 2.375rem;
  line-height: 2.625rem;
  margin-top: 1.3125rem;
  margin-bottom: 1.3125rem;
}

h2 {
  font-size: 1.75rem;
  line-height: 2.625rem;
  margin-top: 1.3125rem;
  margin-bottom: 1.3125rem;
}

h3 {
  font-size: 1.3125rem;
  line-height: 1.3125rem;
  margin-top: 1.3125rem;
  margin-bottom: 0rem;
}

h4 {
  font-size: 1rem;
  line-height: 1.3125rem;
  margin-top: 1.3125rem;
  margin-bottom: 0rem;
}

h5 {
  font-size: 1rem;
  line-height: 1.3125rem;
  margin-top: 1.3125rem;
  margin-bottom: 0rem;
}

p, ul {
  padding-left: 0;
  margin-top: 0rem;
  margin-bottom: 1.3125rem;
}

ul ul, ol ol, ul ol, ol ul {
  margin-top: 0rem;
  margin-bottom: 0rem;
}

pre {
  margin: 0;
}

.right {
  float: right;
  white-space: nowrap;
}

.hidden {
  display: none;
}

#split-grid {
  position: absolute;
  height: 100%;
  left: 0;
  right: 0;
  top: 0;
}

.CodeMirror, .output {
  position: absolute;
  height: calc(100% - 2.625rem);
  top: 2.625rem;
  left: 0;
  right: 0;
}

.output {
  padding: 0.3125rem;
  overflow: auto;
}

.toolbar {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  color: #555;
  background-color: #eee;
  z-index: 9;
  height: 2.625rem;
  line-height: 2rem;
  padding: 0.3125rem 0.3125rem 0;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.3);
}

.split-horizontal, .gutter-horizontal {
  height: 100%;
  float: left;
}

.gutter-horizontal {
  cursor: ew-resize;
  background-image: url('third_party/split/vertical.png');
}

.gutter-vertical {
  cursor: ns-resize;
  background-image: url('third_party/split/horizontal.png');
}

.gutter {
  touch-action: none;
  user-select: none;
  background-color: #ddd;
  background-repeat: no-repeat;
  background-position: 50%;
  z-index: 0;
}

.split {
  position: relative;
  overflow: hidden;
}


body {
  display: flex;
  min-height: 100vh;
  flex-direction: column;
}

main {
  flex: 1;
  position: relative;
}

header {
  padding: 0 1em;
  z-index: 10;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.3);
}

ul {
  margin: 1em;
  list-style: none;
}
