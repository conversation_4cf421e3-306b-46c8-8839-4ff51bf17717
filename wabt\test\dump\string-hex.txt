;;; TOOL: run-objdump
;;; ARGS0: -v
(module (func) (export "foo\de\ad\ca\bb" (func 0)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Export" (7)
0000012: 07                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num exports
0000015: 07                                        ; string length
0000016: 666f 6fde adca bb                        foo....  ; export name
000001d: 00                                        ; export kind
000001e: 00                                        ; export func index
0000013: 0b                                        ; FIXUP section size
; section "Code" (10)
000001f: 0a                                        ; section code
0000020: 00                                        ; section size (guess)
0000021: 01                                        ; num functions
; function body 0
0000022: 00                                        ; func body size (guess)
0000023: 00                                        ; local decl count
0000024: 0b                                        ; end
0000022: 02                                        ; FIXUP func body size
0000020: 04                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

string-hex.wasm:	file format wasm 0x1

Code Disassembly:

000023 func[0] <fooޭʻ>:
 000024: 0b                         | end
;;; STDOUT ;;)
