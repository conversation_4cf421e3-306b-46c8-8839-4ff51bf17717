;;; TOOL: run-gen-wasm
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(FUNCTION) { count[1] type[0] }
section(EXPORT) { count[1] str("main") func_kind func[0] }
section(CODE) {
  count[1]
  func {
    locals[0]
    i32.const
    leb_i32(-420)
    return
  }
}
(;; STDOUT ;;;
(module
  (type (;0;) (func (result i32)))
  (func (;0;) (type 0) (result i32)
    i32.const -420
    return)
  (export "main" (func 0)))
;;; STDOUT ;;)
