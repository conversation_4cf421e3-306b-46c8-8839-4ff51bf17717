;;; TOOL: run-gen-wasm
magic
version
section(TYPE) { count[1] function params[0] results[0] }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func { locals[decl_count[1] i32_count[2] i32] }
}
section("name") {
  section(NAME_LOCALS) {
    func_count[1]
    index[0]
    local_count[2]
    index[0]
    str("L")
    index[1]
    str("L")
  }
}
(;; STDOUT ;;;
(module
  (type (;0;) (func))
  (func (;0;) (type 0)
    (local $L i32) (local $L.1 i32)))
;;; STDOUT ;;)
