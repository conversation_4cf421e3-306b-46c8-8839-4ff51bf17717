;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func (param i32 i64 f32 f64)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 04                                        ; num params
000000d: 7f                                        ; i32
000000e: 7e                                        ; i64
000000f: 7d                                        ; f32
0000010: 7c                                        ; f64
0000011: 00                                        ; num results
0000009: 08                                        ; FIXUP section size
; section "Function" (3)
0000012: 03                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
0000015: 00                                        ; function 0 signature index
0000013: 02                                        ; FIXUP section size
; section "Code" (10)
0000016: 0a                                        ; section code
0000017: 00                                        ; section size (guess)
0000018: 01                                        ; num functions
; function body 0
0000019: 00                                        ; func body size (guess)
000001a: 00                                        ; local decl count
000001b: 0b                                        ; end
0000019: 02                                        ; FIXUP func body size
0000017: 04                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

param-multi.wasm:	file format wasm 0x1

Code Disassembly:

00001a func[0]:
 00001b: 0b                         | end
;;; STDOUT ;;)
