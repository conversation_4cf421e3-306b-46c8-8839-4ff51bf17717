/*
 * Copyright 2017 WebAssembly Community Group participants
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef WABT_FEATURE
#error "You must define WABT_FEATURE before including this file."
#endif

/*
 *           variable          flag                       default  help
 * ========================================================================= */

WABT_FEATURE(exceptions,          "exceptions",              false,   "Experimental exception handling")
WABT_FEATURE(mutable_globals,     "mutable-globals",         true,    "Import/export mutable globals")
WABT_FEATURE(sat_float_to_int,    "saturating-float-to-int", true,    "Saturating float-to-int operators")
WABT_FEATURE(sign_extension,      "sign-extension",          true,    "Sign-extension operators")
WABT_FEATURE(simd,                "simd",                    true,    "SIMD support")
WABT_FEATURE(threads,             "threads",                 false,   "Threading support")
WABT_FEATURE(function_references, "function-references",     false,   "Typed function references")
WABT_FEATURE(multi_value,         "multi-value",             true,    "Multi-value")
WABT_FEATURE(tail_call,           "tail-call",               false,   "Tail-call support")
WABT_FEATURE(bulk_memory,         "bulk-memory",             true,    "Bulk-memory operations")
WABT_FEATURE(reference_types,     "reference-types",         true,    "Reference types (externref)")
WABT_FEATURE(annotations,         "annotations",             false,   "Custom annotation syntax")
WABT_FEATURE(code_metadata,       "code-metadata",           false,   "Code metadata")
WABT_FEATURE(gc,                  "gc",                      false,   "Garbage collection")
WABT_FEATURE(memory64,            "memory64",                false,   "64-bit memory")
WABT_FEATURE(multi_memory,        "multi-memory",            false,   "Multi-memory")
WABT_FEATURE(extended_const,      "extended-const",          false,   "Extended constant expressions")
