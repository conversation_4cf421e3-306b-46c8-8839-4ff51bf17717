;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    i32.const 1
    if
      i32.const 2
      drop
      i32.const 3
      drop
    else
      i32.const 4
      drop
      i32.const 5
      drop
    end))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 00                                        ; local decl count
0000017: 41                                        ; i32.const
0000018: 01                                        ; i32 literal
0000019: 04                                        ; if
000001a: 40                                        ; void
000001b: 41                                        ; i32.const
000001c: 02                                        ; i32 literal
000001d: 1a                                        ; drop
000001e: 41                                        ; i32.const
000001f: 03                                        ; i32 literal
0000020: 1a                                        ; drop
0000021: 05                                        ; else
0000022: 41                                        ; i32.const
0000023: 04                                        ; i32 literal
0000024: 1a                                        ; drop
0000025: 41                                        ; i32.const
0000026: 05                                        ; i32 literal
0000027: 1a                                        ; drop
0000028: 0b                                        ; end
0000029: 0b                                        ; end
0000015: 14                                        ; FIXUP func body size
0000013: 16                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

if-then-else-list.wasm:	file format wasm 0x1

Code Disassembly:

000016 func[0]:
 000017: 41 01                      | i32.const 1
 000019: 04 40                      | if
 00001b: 41 02                      |   i32.const 2
 00001d: 1a                         |   drop
 00001e: 41 03                      |   i32.const 3
 000020: 1a                         |   drop
 000021: 05                         | else
 000022: 41 04                      |   i32.const 4
 000024: 1a                         |   drop
 000025: 41 05                      |   i32.const 5
 000027: 1a                         |   drop
 000028: 0b                         | end
 000029: 0b                         | end
;;; STDOUT ;;)
