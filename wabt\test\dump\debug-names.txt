;;; TOOL: run-objdump
;;; ARGS0: -v --debug-names
;;; ARGS1: -x
(module
  (func $F1 (param $F1P0 i32)
    (local $F1L1 f32)
    (local $F1L2 i32)
    (local i32))

  ;; An unnamed function with a named param
  (func (param $F2P0 f32))

  (func $F2 (param $F3P0 f32)
    (local $F3L1 f64)
    (local i64)
    (local $F3L3 i64)))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 02                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 01                                        ; num params
000000d: 7f                                        ; i32
000000e: 00                                        ; num results
; func type 1
000000f: 60                                        ; func
0000010: 01                                        ; num params
0000011: 7d                                        ; f32
0000012: 00                                        ; num results
0000009: 09                                        ; FIXUP section size
; section "Function" (3)
0000013: 03                                        ; section code
0000014: 00                                        ; section size (guess)
0000015: 03                                        ; num functions
0000016: 00                                        ; function 0 signature index
0000017: 01                                        ; function 1 signature index
0000018: 01                                        ; function 2 signature index
0000014: 04                                        ; FIXUP section size
; section "Code" (10)
0000019: 0a                                        ; section code
000001a: 00                                        ; section size (guess)
000001b: 03                                        ; num functions
; function body 0
000001c: 00                                        ; func body size (guess)
000001d: 02                                        ; local decl count
000001e: 01                                        ; local type count
000001f: 7d                                        ; f32
0000020: 02                                        ; local type count
0000021: 7f                                        ; i32
0000022: 0b                                        ; end
000001c: 06                                        ; FIXUP func body size
; function body 1
0000023: 00                                        ; func body size (guess)
0000024: 00                                        ; local decl count
0000025: 0b                                        ; end
0000023: 02                                        ; FIXUP func body size
; function body 2
0000026: 00                                        ; func body size (guess)
0000027: 02                                        ; local decl count
0000028: 01                                        ; local type count
0000029: 7c                                        ; f64
000002a: 02                                        ; local type count
000002b: 7e                                        ; i64
000002c: 0b                                        ; end
0000026: 06                                        ; FIXUP func body size
000001a: 12                                        ; FIXUP section size
; section "name"
000002d: 00                                        ; section code
000002e: 00                                        ; section size (guess)
000002f: 04                                        ; string length
0000030: 6e61 6d65                                name  ; custom section name
0000034: 01                                        ; name subsection type
0000035: 00                                        ; subsection size (guess)
0000036: 02                                        ; num names
0000037: 00                                        ; elem index
0000038: 02                                        ; string length
0000039: 4631                                     F1  ; elem name 0
000003b: 02                                        ; elem index
000003c: 02                                        ; string length
000003d: 4632                                     F2  ; elem name 2
0000035: 09                                        ; FIXUP subsection size
000003f: 02                                        ; local name type
0000040: 00                                        ; subsection size (guess)
0000041: 03                                        ; num functions
0000042: 00                                        ; function index
0000043: 03                                        ; num locals
0000044: 00                                        ; local index
0000045: 04                                        ; string length
0000046: 4631 5030                                F1P0  ; local name 0
000004a: 01                                        ; local index
000004b: 04                                        ; string length
000004c: 4631 4c31                                F1L1  ; local name 1
0000050: 02                                        ; local index
0000051: 04                                        ; string length
0000052: 4631 4c32                                F1L2  ; local name 2
0000056: 01                                        ; function index
0000057: 01                                        ; num locals
0000058: 00                                        ; local index
0000059: 04                                        ; string length
000005a: 4632 5030                                F2P0  ; local name 0
000005e: 02                                        ; function index
000005f: 03                                        ; num locals
0000060: 00                                        ; local index
0000061: 04                                        ; string length
0000062: 4633 5030                                F3P0  ; local name 0
0000066: 01                                        ; local index
0000067: 04                                        ; string length
0000068: 4633 4c31                                F3L1  ; local name 1
000006c: 03                                        ; local index
000006d: 04                                        ; string length
000006e: 4633 4c33                                F3L3  ; local name 3
0000040: 31                                        ; FIXUP subsection size
000002e: 43                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

debug-names.wasm:	file format wasm 0x1

Section Details:

Type[2]:
 - type[0] (i32) -> nil
 - type[1] (f32) -> nil
Function[3]:
 - func[0] sig=0 <F1>
 - func[1] sig=1
 - func[2] sig=1 <F2>
Code[3]:
 - func[0] size=6 <F1>
 - func[1] size=2
 - func[2] size=6 <F2>
Custom:
 - name: "name"
 - func[0] <F1>
 - func[2] <F2>
 - func[0] local[0] <F1P0>
 - func[0] local[1] <F1L1>
 - func[0] local[2] <F1L2>
 - func[1] local[0] <F2P0>
 - func[2] local[0] <F3P0>
 - func[2] local[1] <F3L1>
 - func[2] local[3] <F3L3>

Code Disassembly:

00001d func[0] <F1>:
 00001e: 01 7d                      | local[1] type=f32
 000020: 02 7f                      | local[2..3] type=i32
 000022: 0b                         | end
000024 func[1]:
 000025: 0b                         | end
000027 func[2] <F2>:
 000028: 01 7c                      | local[0] type=f64
 00002a: 02 7e                      | local[1..2] type=i64
 00002c: 0b                         | end
;;; STDOUT ;;)
