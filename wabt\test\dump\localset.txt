;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func
    ;; i32               0   1
    ;; i64           2                   3
    ;; f32       4               5
    ;; f64   6                       7
    (local f64 f32 i64 i32 i32 f32 f64 i64)
    f64.const 0
    local.set 0
    f32.const 0
    local.set 1
    i64.const 0
    local.set 2
    i32.const 0
    local.set 3
    i32.const 0
    local.set 4
    f32.const 0
    local.set 5
    f64.const 0
    local.set 6
    i64.const 0
    local.set 7))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Code" (10)
0000012: 0a                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num functions
; function body 0
0000015: 00                                        ; func body size (guess)
0000016: 07                                        ; local decl count
0000017: 01                                        ; local type count
0000018: 7c                                        ; f64
0000019: 01                                        ; local type count
000001a: 7d                                        ; f32
000001b: 01                                        ; local type count
000001c: 7e                                        ; i64
000001d: 02                                        ; local type count
000001e: 7f                                        ; i32
000001f: 01                                        ; local type count
0000020: 7d                                        ; f32
0000021: 01                                        ; local type count
0000022: 7c                                        ; f64
0000023: 01                                        ; local type count
0000024: 7e                                        ; i64
0000025: 44                                        ; f64.const
0000026: 0000 0000 0000 0000                       ; f64 literal
000002e: 21                                        ; local.set
000002f: 00                                        ; local index
0000030: 43                                        ; f32.const
0000031: 0000 0000                                 ; f32 literal
0000035: 21                                        ; local.set
0000036: 01                                        ; local index
0000037: 42                                        ; i64.const
0000038: 00                                        ; i64 literal
0000039: 21                                        ; local.set
000003a: 02                                        ; local index
000003b: 41                                        ; i32.const
000003c: 00                                        ; i32 literal
000003d: 21                                        ; local.set
000003e: 03                                        ; local index
000003f: 41                                        ; i32.const
0000040: 00                                        ; i32 literal
0000041: 21                                        ; local.set
0000042: 04                                        ; local index
0000043: 43                                        ; f32.const
0000044: 0000 0000                                 ; f32 literal
0000048: 21                                        ; local.set
0000049: 05                                        ; local index
000004a: 44                                        ; f64.const
000004b: 0000 0000 0000 0000                       ; f64 literal
0000053: 21                                        ; local.set
0000054: 06                                        ; local index
0000055: 42                                        ; i64.const
0000056: 00                                        ; i64 literal
0000057: 21                                        ; local.set
0000058: 07                                        ; local index
0000059: 0b                                        ; end
0000015: 44                                        ; FIXUP func body size
0000013: 46                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

localset.wasm:	file format wasm 0x1

Code Disassembly:

000016 func[0]:
 000017: 01 7c                      | local[0] type=f64
 000019: 01 7d                      | local[1] type=f32
 00001b: 01 7e                      | local[2] type=i64
 00001d: 02 7f                      | local[3..4] type=i32
 00001f: 01 7d                      | local[5] type=f32
 000021: 01 7c                      | local[6] type=f64
 000023: 01 7e                      | local[7] type=i64
 000025: 44 00 00 00 00 00 00 00 00 | f64.const 0x0p+0
 00002e: 21 00                      | local.set 0
 000030: 43 00 00 00 00             | f32.const 0x0p+0
 000035: 21 01                      | local.set 1
 000037: 42 00                      | i64.const 0
 000039: 21 02                      | local.set 2
 00003b: 41 00                      | i32.const 0
 00003d: 21 03                      | local.set 3
 00003f: 41 00                      | i32.const 0
 000041: 21 04                      | local.set 4
 000043: 43 00 00 00 00             | f32.const 0x0p+0
 000048: 21 05                      | local.set 5
 00004a: 44 00 00 00 00 00 00 00 00 | f64.const 0x0p+0
 000053: 21 06                      | local.set 6
 000055: 42 00                      | i64.const 0
 000057: 21 07                      | local.set 7
 000059: 0b                         | end
;;; STDOUT ;;)
