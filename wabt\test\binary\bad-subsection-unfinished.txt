;;; TOOL: run-gen-wasm-bad
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(FUNCTION) { count[1] type[0] }
section(CODE) {
  count[1]
  func {
    locals[decl_count[1] i32_count[1] i32]
    local.get 0
  }
}
section("name") {
  section(NAME_FUNCTION) {
    func_count[1]
    index[0]
    str("$F0")

    data[1]  ;; extra unused data
  }
}
(;; STDERR ;;;
000002c: error: unfinished sub-section (expected end: 0x2d)
000002c: error: unfinished sub-section (expected end: 0x2d)
;;; STDERR ;;)
