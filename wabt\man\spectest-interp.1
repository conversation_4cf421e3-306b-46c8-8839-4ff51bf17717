.Dd $Mdocdate$
.Dt WABT 1
.Os
.Sh NAME
.Nm spectest-interp
.Nd read a Spectest JSON file, and run its tests in the interpreter
.Sh SYNOPSIS
.Nm spectest-interp
.Op options
.Ar file
.Sh DESCRIPTION
.Nm
reads a Spectest JSON file, and runs its tests in the interpreter.
.Pp
The options are as follows:
.Bl -tag -width Ds
.It Fl v , Fl Fl verbose
Use multiple times for more info
.It Fl Fl help
Print this help message
.It Fl Fl enable-exceptions
Enable Experimental exception handling
.It Fl Fl disable-mutable-globals
Disable Import/export mutable globals
.It Fl Fl enable-saturating-float-to-int
Enable Saturating float-to-int operators
.It Fl Fl enable-sign-extension
Enable Sign-extension operators
.It Fl Fl disable-simd
Disable SIMD support
.It Fl Fl enable-threads
Enable Threading support
.It Fl Fl enable-multi-value
Enable Multi-value
.It Fl Fl enable-tail-call
Enable Tail-call support
.It Fl V , Fl Fl value-stack-size=SIZE
Size in elements of the value stack
.It Fl C , Fl Fl call-stack-size=SIZE
Size in elements of the call stack
.It Fl t , Fl Fl trace
Trace execution
.El
.Sh EXAMPLES
Parse test.json and run the spec tests
.Pp
.Dl $ spectest-interp test.json
.Pp
.Sh SEE ALSO
.Xr wasm-interp 1 ,
.Xr wasm-objdump 1 ,
.Xr wasm-opcodecnt 1 ,
.Xr wasm-strip 1 ,
.Xr wasm-validate 1 ,
.Xr wasm2c 1 ,
.Xr wasm2wat 1 ,
.Xr wast2json 1 ,
.Xr wat-desugar 1 ,
.Xr wat2wasm 1
.Sh BUGS
If you find a bug, please report it at
.br
.Lk https://github.com/WebAssembly/wabt/issues .
