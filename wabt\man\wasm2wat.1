.Dd $Mdocdate$
.Dt WABT 1
.Os
.Sh NAME
.Nm wasm2wat
.Nd translate from the binary format to the text format
.Sh SYNOPSIS
.Nm wasm2wat
.Op options
.Ar file
.Sh DESCRIPTION
.Nm
does the inverse of wat2wasm, translate from the binary format back to the text format (also known as a .wat).
.Pp
The options are as follows:
.Bl -tag -width Ds
.It Fl v , Fl Fl verbose
Use multiple times for more info
.It Fl Fl help
Print a help message
.It Fl o , Fl Fl output=FILENAME
Output file for the generated wast file, by default use stdout
.It Fl f , Fl Fl fold-exprs
Write folded expressions where possible
.It Fl Fl enable-exceptions
Experimental exception handling
.It Fl Fl disable-mutable-globals
Import/export mutable globals
.It Fl Fl enable-saturating-float-to-int
Saturating float-to-int operators
.It Fl Fl enable-sign-extension
Sign-extension operators
.It Fl Fl disable-simd
SIMD support
.It Fl Fl enable-threads
Threading support
.It Fl Fl inline-exports
Write all exports inline
.It Fl Fl inline-imports
Write all imports inline
.It Fl Fl no-debug-names
Ignore debug names in the binary file
.It Fl Fl generate-names
Give auto-generated names to non-named functions, types, etc.
.It Fl Fl no-check
Don't check for invalid modules
.El
.Sh EXAMPLES
Parse binary file test.wasm and write text file test.wast
.Pp
.Dl $ wasm2wat test.wasm -o test.wat
.Pp
Parse test.wasm, write test.wat, but ignore the debug names, if any
.Pp
.Dl $ wasm2wat test.wasm --no-debug-names -o test.wat
.Sh SEE ALSO
.Xr wasm-interp 1 ,
.Xr wasm-objdump 1 ,
.Xr wasm-opcodecnt 1 ,
.Xr wasm-strip 1 ,
.Xr wasm-validate 1 ,
.Xr wasm2c 1 ,
.Xr wast2json 1 ,
.Xr wat-desugar 1 ,
.Xr wat2wasm 1 ,
.Xr spectest-interp 1
.Sh BUGS
If you find a bug, please report it at
.br
.Lk https://github.com/WebAssembly/wabt/issues .
