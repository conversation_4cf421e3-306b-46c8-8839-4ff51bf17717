;;; TOOL: wat2wasm
;;; ARGS: -v --no-check
(module
  (table 1 funcref)
  (func)
  (elem (i32.eqz (i32.const 1)) 0))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Table" (4)
0000012: 04                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num tables
; table 0
0000015: 70                                        ; funcref
0000016: 00                                        ; limits: flags
0000017: 01                                        ; limits: initial
0000013: 04                                        ; FIXUP section size
; section "Elem" (9)
0000018: 09                                        ; section code
0000019: 00                                        ; section size (guess)
000001a: 01                                        ; num elem segments
; elem segment header 0
000001b: 00                                        ; segment flags
000001c: 41                                        ; i32.const
000001d: 01                                        ; i32 literal
000001e: 45                                        ; i32.eqz
000001f: 0b                                        ; end
0000020: 01                                        ; num elems
0000021: 00                                        ; elem function index
0000019: 08                                        ; FIXUP section size
; section "Code" (10)
0000022: 0a                                        ; section code
0000023: 00                                        ; section size (guess)
0000024: 01                                        ; num functions
; function body 0
0000025: 00                                        ; func body size (guess)
0000026: 00                                        ; local decl count
0000027: 0b                                        ; end
0000025: 02                                        ; FIXUP func body size
0000023: 04                                        ; FIXUP section size
;;; STDERR ;;)
