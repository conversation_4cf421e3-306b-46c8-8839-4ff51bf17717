;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (memory 1)
  (func
    i32.const 0
    i32.const 0
    i32.store8
    i32.const 0
    i32.const 0
    i32.store16
    i32.const 0
    i32.const 0
    i32.store
    i32.const 0
    i64.const 0
    i64.store
    i32.const 0
    i64.const 0
    i64.store8
    i32.const 0
    i64.const 0
    i64.store16
    i32.const 0
    i64.const 0
    i64.store32
    i32.const 0
    f32.const 0
    f32.store
    i32.const 0
    f64.const 0
    f64.store))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Memory" (5)
0000012: 05                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num memories
; memory 0
0000015: 00                                        ; limits: flags
0000016: 01                                        ; limits: initial
0000013: 03                                        ; FIXUP section size
; section "Code" (10)
0000017: 0a                                        ; section code
0000018: 00                                        ; section size (guess)
0000019: 01                                        ; num functions
; function body 0
000001a: 00                                        ; func body size (guess)
000001b: 00                                        ; local decl count
000001c: 41                                        ; i32.const
000001d: 00                                        ; i32 literal
000001e: 41                                        ; i32.const
000001f: 00                                        ; i32 literal
0000020: 3a                                        ; i32.store8
0000021: 00                                        ; alignment
0000022: 00                                        ; store offset
0000023: 41                                        ; i32.const
0000024: 00                                        ; i32 literal
0000025: 41                                        ; i32.const
0000026: 00                                        ; i32 literal
0000027: 3b                                        ; i32.store16
0000028: 01                                        ; alignment
0000029: 00                                        ; store offset
000002a: 41                                        ; i32.const
000002b: 00                                        ; i32 literal
000002c: 41                                        ; i32.const
000002d: 00                                        ; i32 literal
000002e: 36                                        ; i32.store
000002f: 02                                        ; alignment
0000030: 00                                        ; store offset
0000031: 41                                        ; i32.const
0000032: 00                                        ; i32 literal
0000033: 42                                        ; i64.const
0000034: 00                                        ; i64 literal
0000035: 37                                        ; i64.store
0000036: 03                                        ; alignment
0000037: 00                                        ; store offset
0000038: 41                                        ; i32.const
0000039: 00                                        ; i32 literal
000003a: 42                                        ; i64.const
000003b: 00                                        ; i64 literal
000003c: 3c                                        ; i64.store8
000003d: 00                                        ; alignment
000003e: 00                                        ; store offset
000003f: 41                                        ; i32.const
0000040: 00                                        ; i32 literal
0000041: 42                                        ; i64.const
0000042: 00                                        ; i64 literal
0000043: 3d                                        ; i64.store16
0000044: 01                                        ; alignment
0000045: 00                                        ; store offset
0000046: 41                                        ; i32.const
0000047: 00                                        ; i32 literal
0000048: 42                                        ; i64.const
0000049: 00                                        ; i64 literal
000004a: 3e                                        ; i64.store32
000004b: 02                                        ; alignment
000004c: 00                                        ; store offset
000004d: 41                                        ; i32.const
000004e: 00                                        ; i32 literal
000004f: 43                                        ; f32.const
0000050: 0000 0000                                 ; f32 literal
0000054: 38                                        ; f32.store
0000055: 02                                        ; alignment
0000056: 00                                        ; store offset
0000057: 41                                        ; i32.const
0000058: 00                                        ; i32 literal
0000059: 44                                        ; f64.const
000005a: 0000 0000 0000 0000                       ; f64 literal
0000062: 39                                        ; f64.store
0000063: 03                                        ; alignment
0000064: 00                                        ; store offset
0000065: 0b                                        ; end
000001a: 4b                                        ; FIXUP func body size
0000018: 4d                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

store.wasm:	file format wasm 0x1

Code Disassembly:

00001b func[0]:
 00001c: 41 00                      | i32.const 0
 00001e: 41 00                      | i32.const 0
 000020: 3a 00 00                   | i32.store8 0 0
 000023: 41 00                      | i32.const 0
 000025: 41 00                      | i32.const 0
 000027: 3b 01 00                   | i32.store16 1 0
 00002a: 41 00                      | i32.const 0
 00002c: 41 00                      | i32.const 0
 00002e: 36 02 00                   | i32.store 2 0
 000031: 41 00                      | i32.const 0
 000033: 42 00                      | i64.const 0
 000035: 37 03 00                   | i64.store 3 0
 000038: 41 00                      | i32.const 0
 00003a: 42 00                      | i64.const 0
 00003c: 3c 00 00                   | i64.store8 0 0
 00003f: 41 00                      | i32.const 0
 000041: 42 00                      | i64.const 0
 000043: 3d 01 00                   | i64.store16 1 0
 000046: 41 00                      | i32.const 0
 000048: 42 00                      | i64.const 0
 00004a: 3e 02 00                   | i64.store32 2 0
 00004d: 41 00                      | i32.const 0
 00004f: 43 00 00 00 00             | f32.const 0x0p+0
 000054: 38 02 00                   | f32.store 2 0
 000057: 41 00                      | i32.const 0
 000059: 44 00 00 00 00 00 00 00 00 | f64.const 0x0p+0
 000062: 39 03 00                   | f64.store 3 0
 000065: 0b                         | end
;;; STDOUT ;;)
