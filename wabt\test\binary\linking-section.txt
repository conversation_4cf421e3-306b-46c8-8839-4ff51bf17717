;;; TOOL: run-objdump-gen-wasm
;;; ARGS: -x
magic
version
section(MEMORY) {
  count[1]
  has_max[0]
  initial[0]
}
section(DATA) {
  count[2]
  memory_index[0]
  offset[i32.const 0 end]
  data[str("foo")]
  memory_index[0]
  offset[i32.const 10 end]
  data[str("bar")]
}
section("linking") {
  metadata_version[2]

  section(LINKING_SEGMENT_INFO) {
    count[2]
    str("data1")
    p2align[2]
    flags[1]
    str("data2")
    p2align[3]
    flags[10]
  }

  section(LINKING_INIT_FUNCTIONS) {
    count[2]
    priority[5]
    func[1]
    priority[6]
    func[0]
  }

  section(LINKING_SYMBOL_TABLE) {
    num_symbols[4]

    type[0]
    flags[1]
    index[0]
    str("func_sym")

    type[2]
    flags[2]
    index[0]
    str("global_sym")

    type[1]
    flags[leb_i32(260)]
    str("data_sym")
    segment[1]
    offset[2]
    size[1]

    type[0]
    flags[0x10]
    index[0]
  }

  section(LINKING_COMDAT_INFO) {
    num_comdats[2]

    str("comdat1")
    flags[0]
    num_entries[1]

    kind[0]
    index[1]

    str("comdat2")
    flags[0]
    num_entries[2]

    kind[1]
    index[2]

    kind[1]
    index[3]
  }
}
(;; STDOUT ;;;

linking-section.wasm:	file format wasm 0x1

Section Details:

Memory[1]:
 - memory[0] pages: initial=0
Data[2]:
 - segment[0] <data1> memory=0 size=3 - init i32=0
  - 0000000: 666f 6f                                  foo
 - segment[1] <data2> memory=0 size=3 - init i32=10
  - 000000a: 6261 72                                  bar
Custom:
 - name: "linking"
  - segment info [count=2]
   - 0: data1 p2align=2 [ STRINGS ]
   - 1: data2 p2align=3 [ TLS unknown_flags=0x8 ]
  - init functions [count=2]
   - 1: priority=5
   - 0: priority=6
  - symbol table [count=4]
   - 0: F <func_sym> func=0 [ binding=weak vis=default ]
   - 1: G <global_sym> global=0 [ binding=local vis=default ]
   - 2: D <data_sym> segment=1 offset=2 size=1 [ tls binding=global vis=hidden ]
   - 3: F <func_sym> func=0 [ undefined binding=global vis=default ]
  - comdat groups [count=2]
   - comdat1: [count=1]
    - segment[1] <data2>
   - comdat2: [count=2]
    - func[2]
    - func[3]

Code Disassembly:

;;; STDOUT ;;)
