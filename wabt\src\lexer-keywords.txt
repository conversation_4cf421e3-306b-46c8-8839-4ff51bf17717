struct TokenInfo {
  TokenInfo(const char* name) : name(name) {}
  TokenInfo(const char* name, TokenType token_type)
      : name(name), token_type(token_type) {}
  TokenInfo(const char* name, Type value_type)
      : name(name), token_type(TokenType::ValueType), value_type(value_type) {}
  TokenInfo(const char* name, Type value_type, TokenType token_type)
      : name(name), token_type(token_type), value_type(value_type) {}
  TokenInfo(const char* name, TokenType token_type, Opcode opcode)
      : name(name), token_type(token_type), opcode(opcode) {}

  const char* name;
  TokenType token_type;
  union {
    Type value_type;
    Opcode opcode;
  };
};
%%
array, Type::Array, TokenType::Array
assert_exception, TokenType::AssertException
assert_exhaustion, TokenType::AssertExhaustion
assert_invalid, TokenType::AssertInvalid
assert_malformed, TokenType::AssertMalformed
assert_return, TokenType::AssertReturn
assert_trap, TokenType::AssertTrap
assert_unlinkable, TokenType::AssertUnlinkable
atomic.fence, TokenType::AtomicFence, Opcode::AtomicFence
binary, TokenType::Bin
block, TokenType::Block, Opcode::Block
br_if, TokenType::BrIf, Opcode::BrIf
br_table, TokenType::BrTable, Opcode::BrTable
br, TokenType::Br, Opcode::Br
call_indirect, TokenType::CallIndirect, Opcode::CallIndirect
call_ref, TokenType::CallRef, Opcode::CallRef
call, TokenType::Call, Opcode::Call
catch, TokenType::Catch, Opcode::Catch
catch_all, TokenType::CatchAll, Opcode::CatchAll
data.drop, TokenType::DataDrop, Opcode::DataDrop
data, TokenType::Data
declare, TokenType::Declare
delegate, TokenType::Delegate
do, TokenType::Do
drop, TokenType::Drop, Opcode::Drop
elem.drop, TokenType::ElemDrop, Opcode::ElemDrop
elem, TokenType::Elem
else, TokenType::Else, Opcode::Else
end, TokenType::End, Opcode::End
tag, TokenType::Tag
extern, Type::ExternRef, TokenType::Extern
externref, Type::ExternRef
export, TokenType::Export
f32.abs, TokenType::Unary, Opcode::F32Abs
f32.add, TokenType::Binary, Opcode::F32Add
f32.ceil, TokenType::Unary, Opcode::F32Ceil
f32.const, TokenType::Const, Opcode::F32Const
f32.convert_i32_s, TokenType::Convert, Opcode::F32ConvertI32S
f32.convert_i32_u, TokenType::Convert, Opcode::F32ConvertI32U
f32.convert_i64_s, TokenType::Convert, Opcode::F32ConvertI64S
f32.convert_i64_u, TokenType::Convert, Opcode::F32ConvertI64U
f32.copysign, TokenType::Binary, Opcode::F32Copysign
f32.demote_f64, TokenType::Convert, Opcode::F32DemoteF64
f32.div, TokenType::Binary, Opcode::F32Div
f32.eq, TokenType::Compare, Opcode::F32Eq
f32.floor, TokenType::Unary, Opcode::F32Floor
f32.ge, TokenType::Compare, Opcode::F32Ge
f32.gt, TokenType::Compare, Opcode::F32Gt
f32.le, TokenType::Compare, Opcode::F32Le
f32.load, TokenType::Load, Opcode::F32Load
f32.lt, TokenType::Compare, Opcode::F32Lt
f32.max, TokenType::Binary, Opcode::F32Max
f32.min, TokenType::Binary, Opcode::F32Min
f32.mul, TokenType::Binary, Opcode::F32Mul
f32.nearest, TokenType::Unary, Opcode::F32Nearest
f32.neg, TokenType::Unary, Opcode::F32Neg
f32.ne, TokenType::Compare, Opcode::F32Ne
f32.reinterpret_i32, TokenType::Convert, Opcode::F32ReinterpretI32
f32.sqrt, TokenType::Unary, Opcode::F32Sqrt
f32.store, TokenType::Store, Opcode::F32Store
f32.sub, TokenType::Binary, Opcode::F32Sub
f32.trunc, TokenType::Unary, Opcode::F32Trunc
f32, Type::F32
f32x4.abs, TokenType::Unary, Opcode::F32X4Abs
f32x4.add, TokenType::Binary, Opcode::F32X4Add
f32x4.ceil, TokenType::Unary, Opcode::F32X4Ceil
f32x4.convert_i32x4_s, TokenType::Unary, Opcode::F32X4ConvertI32X4S
f32x4.convert_i32x4_u, TokenType::Unary, Opcode::F32X4ConvertI32X4U
f32x4.div, TokenType::Binary, Opcode::F32X4Div
f32x4.eq, TokenType::Compare, Opcode::F32X4Eq
f32x4.extract_lane, TokenType::SimdLaneOp, Opcode::F32X4ExtractLane
f32x4.floor, TokenType::Unary, Opcode::F32X4Floor
f32x4.ge, TokenType::Compare, Opcode::F32X4Ge
f32x4.gt, TokenType::Compare, Opcode::F32X4Gt
f32x4.le, TokenType::Compare, Opcode::F32X4Le
f32x4.lt, TokenType::Compare, Opcode::F32X4Lt
f32x4.max, TokenType::Binary, Opcode::F32X4Max
f32x4.min, TokenType::Binary, Opcode::F32X4Min
f32x4.mul, TokenType::Binary, Opcode::F32X4Mul
f32x4.nearest, TokenType::Unary, Opcode::F32X4Nearest
f32x4.neg, TokenType::Unary, Opcode::F32X4Neg
f32x4.ne, TokenType::Compare, Opcode::F32X4Ne
f32x4.pmax, TokenType::Binary, Opcode::F32X4PMax
f32x4.pmin, TokenType::Binary, Opcode::F32X4PMin
f32x4.replace_lane, TokenType::SimdLaneOp, Opcode::F32X4ReplaceLane
f32x4.splat, TokenType::Unary, Opcode::F32X4Splat
f32x4.sqrt, TokenType::Unary, Opcode::F32X4Sqrt
f32x4.sub, TokenType::Binary, Opcode::F32X4Sub
f32x4.trunc, TokenType::Unary, Opcode::F32X4Trunc
f32x4.demote_f64x2_zero, TokenType::Unary, Opcode::F32X4DemoteF64X2Zero
f32x4, TokenType::F32X4
f64.abs, TokenType::Unary, Opcode::F64Abs
f64.add, TokenType::Binary, Opcode::F64Add
f64.ceil, TokenType::Unary, Opcode::F64Ceil
f64.const, TokenType::Const, Opcode::F64Const
f64.convert_i32_s, TokenType::Convert, Opcode::F64ConvertI32S
f64.convert_i32_u, TokenType::Convert, Opcode::F64ConvertI32U
f64.convert_i64_s, TokenType::Convert, Opcode::F64ConvertI64S
f64.convert_i64_u, TokenType::Convert, Opcode::F64ConvertI64U
f64.copysign, TokenType::Binary, Opcode::F64Copysign
f64.div, TokenType::Binary, Opcode::F64Div
f64.eq, TokenType::Compare, Opcode::F64Eq
f64.floor, TokenType::Unary, Opcode::F64Floor
f64.ge, TokenType::Compare, Opcode::F64Ge
f64.gt, TokenType::Compare, Opcode::F64Gt
f64.le, TokenType::Compare, Opcode::F64Le
f64.load, TokenType::Load, Opcode::F64Load
f64.lt, TokenType::Compare, Opcode::F64Lt
f64.max, TokenType::Binary, Opcode::F64Max
f64.min, TokenType::Binary, Opcode::F64Min
f64.mul, TokenType::Binary, Opcode::F64Mul
f64.nearest, TokenType::Unary, Opcode::F64Nearest
f64.neg, TokenType::Unary, Opcode::F64Neg
f64.ne, TokenType::Compare, Opcode::F64Ne
f64.promote_f32, TokenType::Convert, Opcode::F64PromoteF32
f64.reinterpret_i64, TokenType::Convert, Opcode::F64ReinterpretI64
f64.sqrt, TokenType::Unary, Opcode::F64Sqrt
f64.store, TokenType::Store, Opcode::F64Store
f64.sub, TokenType::Binary, Opcode::F64Sub
f64.trunc, TokenType::Unary, Opcode::F64Trunc
f64, Type::F64
f64x2.abs, TokenType::Unary, Opcode::F64X2Abs
f64x2.add, TokenType::Binary, Opcode::F64X2Add
f64x2.ceil, TokenType::Unary, Opcode::F64X2Ceil
f64x2.div, TokenType::Binary, Opcode::F64X2Div
f64x2.eq, TokenType::Compare, Opcode::F64X2Eq
f64x2.extract_lane, TokenType::SimdLaneOp, Opcode::F64X2ExtractLane
f64x2.floor, TokenType::Unary, Opcode::F64X2Floor
f64x2.ge, TokenType::Compare, Opcode::F64X2Ge
f64x2.gt, TokenType::Compare, Opcode::F64X2Gt
f64x2.le, TokenType::Compare, Opcode::F64X2Le
f64x2.lt, TokenType::Compare, Opcode::F64X2Lt
f64x2.max, TokenType::Binary, Opcode::F64X2Max
f64x2.min, TokenType::Binary, Opcode::F64X2Min
f64x2.mul, TokenType::Binary, Opcode::F64X2Mul
f64x2.nearest, TokenType::Unary, Opcode::F64X2Nearest
f64x2.neg, TokenType::Unary, Opcode::F64X2Neg
f64x2.ne, TokenType::Compare, Opcode::F64X2Ne
f64x2.pmax, TokenType::Binary, Opcode::F64X2PMax
f64x2.pmin, TokenType::Binary, Opcode::F64X2PMin
f64x2.replace_lane, TokenType::SimdLaneOp, Opcode::F64X2ReplaceLane
f64x2.splat, TokenType::Unary, Opcode::F64X2Splat
f64x2.sqrt, TokenType::Unary, Opcode::F64X2Sqrt
f64x2.sub, TokenType::Binary, Opcode::F64X2Sub
f64x2.trunc, TokenType::Unary, Opcode::F64X2Trunc
f64x2.convert_low_i32x4_s, TokenType::Unary, Opcode::F64X2ConvertLowI32X4S
f64x2.convert_low_i32x4_u, TokenType::Unary, Opcode::F64X2ConvertLowI32X4U
f64x2.promote_low_f32x4, TokenType::Unary, Opcode::F64X2PromoteLowF32X4
f64x2, TokenType::F64X2
field, TokenType::Field
funcref, Type::FuncRef
func, Type::FuncRef, TokenType::Func
get, TokenType::Get
global.get, TokenType::GlobalGet, Opcode::GlobalGet
global.set, TokenType::GlobalSet, Opcode::GlobalSet
global, TokenType::Global
i16x8.abs, TokenType::Unary, Opcode::I16X8Abs
i16x8.add_sat_s, TokenType::Binary, Opcode::I16X8AddSatS
i16x8.add_sat_u, TokenType::Binary, Opcode::I16X8AddSatU
i16x8.add, TokenType::Binary, Opcode::I16X8Add
i16x8.all_true, TokenType::Unary, Opcode::I16X8AllTrue
i16x8.avgr_u, TokenType::Binary, Opcode::I16X8AvgrU
i16x8.bitmask, TokenType::Unary, Opcode::I16X8Bitmask
i16x8.eq, TokenType::Compare, Opcode::I16X8Eq
i16x8.extract_lane_s, TokenType::SimdLaneOp, Opcode::I16X8ExtractLaneS
i16x8.extract_lane_u, TokenType::SimdLaneOp, Opcode::I16X8ExtractLaneU
i16x8.ge_s, TokenType::Compare, Opcode::I16X8GeS
i16x8.ge_u, TokenType::Compare, Opcode::I16X8GeU
i16x8.gt_s, TokenType::Compare, Opcode::I16X8GtS
i16x8.gt_u, TokenType::Compare, Opcode::I16X8GtU
i16x8.le_s, TokenType::Compare, Opcode::I16X8LeS
i16x8.le_u, TokenType::Compare, Opcode::I16X8LeU
v128.load8x8_s, TokenType::Load, Opcode::V128Load8X8S
v128.load8x8_u, TokenType::Load, Opcode::V128Load8X8U
i16x8.lt_s, TokenType::Compare, Opcode::I16X8LtS
i16x8.lt_u, TokenType::Compare, Opcode::I16X8LtU
i16x8.max_s, TokenType::Binary, Opcode::I16X8MaxS
i16x8.max_u, TokenType::Binary, Opcode::I16X8MaxU
i16x8.min_s, TokenType::Binary, Opcode::I16X8MinS
i16x8.min_u, TokenType::Binary, Opcode::I16X8MinU
i16x8.mul, TokenType::Binary, Opcode::I16X8Mul
i16x8.narrow_i32x4_s, TokenType::Binary, Opcode::I16X8NarrowI32X4S
i16x8.narrow_i32x4_u, TokenType::Binary, Opcode::I16X8NarrowI32X4U
i16x8.neg, TokenType::Unary, Opcode::I16X8Neg
i16x8.q15mulr_sat_s, TokenType::Binary, Opcode::I16X8Q15mulrSatS
i16x8.ne, TokenType::Compare, Opcode::I16X8Ne
i16x8.replace_lane, TokenType::SimdLaneOp, Opcode::I16X8ReplaceLane
i16x8.shl, TokenType::Binary, Opcode::I16X8Shl
i16x8.shr_s, TokenType::Binary, Opcode::I16X8ShrS
i16x8.shr_u, TokenType::Binary, Opcode::I16X8ShrU
i16x8.splat, TokenType::Unary, Opcode::I16X8Splat
i16x8.sub_sat_s, TokenType::Binary, Opcode::I16X8SubSatS
i16x8.sub_sat_u, TokenType::Binary, Opcode::I16X8SubSatU
i16x8.sub, TokenType::Binary, Opcode::I16X8Sub
i16x8.extadd_pairwise_i8x16_s, TokenType::Unary, Opcode::I16X8ExtaddPairwiseI8X16S
i16x8.extadd_pairwise_i8x16_u, TokenType::Unary, Opcode::I16X8ExtaddPairwiseI8X16U
i16x8.extmul_low_i8x16_s, TokenType::Binary, Opcode::I16X8ExtmulLowI8X16S
i16x8.extmul_high_i8x16_s, TokenType::Binary, Opcode::I16X8ExtmulHighI8X16S
i16x8.extmul_low_i8x16_u, TokenType::Binary, Opcode::I16X8ExtmulLowI8X16U
i16x8.extmul_high_i8x16_u, TokenType::Binary, Opcode::I16X8ExtmulHighI8X16U
i16x8, TokenType::I16X8
i16x8.extend_high_i8x16_s, TokenType::Unary, Opcode::I16X8ExtendHighI8X16S
i16x8.extend_high_i8x16_u, TokenType::Unary, Opcode::I16X8ExtendHighI8X16U
i16x8.extend_low_i8x16_s, TokenType::Unary, Opcode::I16X8ExtendLowI8X16S
i16x8.extend_low_i8x16_u, TokenType::Unary, Opcode::I16X8ExtendLowI8X16U
i32.add, TokenType::Binary, Opcode::I32Add
i32.and, TokenType::Binary, Opcode::I32And
i32.atomic.load16_u, TokenType::AtomicLoad, Opcode::I32AtomicLoad16U
i32.atomic.load8_u, TokenType::AtomicLoad, Opcode::I32AtomicLoad8U
i32.atomic.load, TokenType::AtomicLoad, Opcode::I32AtomicLoad
i32.atomic.rmw16.add_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw16AddU
i32.atomic.rmw16.and_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw16AndU
i32.atomic.rmw16.cmpxchg_u, TokenType::AtomicRmwCmpxchg, Opcode::I32AtomicRmw16CmpxchgU
i32.atomic.rmw16.or_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw16OrU
i32.atomic.rmw16.sub_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw16SubU
i32.atomic.rmw16.xchg_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw16XchgU
i32.atomic.rmw16.xor_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw16XorU
i32.atomic.rmw8.add_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw8AddU
i32.atomic.rmw8.and_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw8AndU
i32.atomic.rmw8.cmpxchg_u, TokenType::AtomicRmwCmpxchg, Opcode::I32AtomicRmw8CmpxchgU
i32.atomic.rmw8.or_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw8OrU
i32.atomic.rmw8.sub_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw8SubU
i32.atomic.rmw8.xchg_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw8XchgU
i32.atomic.rmw8.xor_u, TokenType::AtomicRmw, Opcode::I32AtomicRmw8XorU
i32.atomic.rmw.add, TokenType::AtomicRmw, Opcode::I32AtomicRmwAdd
i32.atomic.rmw.and, TokenType::AtomicRmw, Opcode::I32AtomicRmwAnd
i32.atomic.rmw.cmpxchg, TokenType::AtomicRmwCmpxchg, Opcode::I32AtomicRmwCmpxchg
i32.atomic.rmw.or, TokenType::AtomicRmw, Opcode::I32AtomicRmwOr
i32.atomic.rmw.sub, TokenType::AtomicRmw, Opcode::I32AtomicRmwSub
i32.atomic.rmw.xchg, TokenType::AtomicRmw, Opcode::I32AtomicRmwXchg
i32.atomic.rmw.xor, TokenType::AtomicRmw, Opcode::I32AtomicRmwXor
i32.atomic.store16, TokenType::AtomicStore, Opcode::I32AtomicStore16
i32.atomic.store8, TokenType::AtomicStore, Opcode::I32AtomicStore8
i32.atomic.store, TokenType::AtomicStore, Opcode::I32AtomicStore
i32.clz, TokenType::Unary, Opcode::I32Clz
i32.const, TokenType::Const, Opcode::I32Const
i32.ctz, TokenType::Unary, Opcode::I32Ctz
i32.div_s, TokenType::Binary, Opcode::I32DivS
i32.div_u, TokenType::Binary, Opcode::I32DivU
i32.eq, TokenType::Compare, Opcode::I32Eq
i32.eqz, TokenType::Convert, Opcode::I32Eqz
i32.extend16_s, TokenType::Unary, Opcode::I32Extend16S
i32.extend8_s, TokenType::Unary, Opcode::I32Extend8S
i32.ge_s, TokenType::Compare, Opcode::I32GeS
i32.ge_u, TokenType::Compare, Opcode::I32GeU
i32.gt_s, TokenType::Compare, Opcode::I32GtS
i32.gt_u, TokenType::Compare, Opcode::I32GtU
i32.le_s, TokenType::Compare, Opcode::I32LeS
i32.le_u, TokenType::Compare, Opcode::I32LeU
i32.load16_s, TokenType::Load, Opcode::I32Load16S
i32.load16_u, TokenType::Load, Opcode::I32Load16U
i32.load8_s, TokenType::Load, Opcode::I32Load8S
i32.load8_u, TokenType::Load, Opcode::I32Load8U
i32.load, TokenType::Load, Opcode::I32Load
i32.lt_s, TokenType::Compare, Opcode::I32LtS
i32.lt_u, TokenType::Compare, Opcode::I32LtU
i32.mul, TokenType::Binary, Opcode::I32Mul
i32.ne, TokenType::Compare, Opcode::I32Ne
i32.or, TokenType::Binary, Opcode::I32Or
i32.popcnt, TokenType::Unary, Opcode::I32Popcnt
i32.reinterpret_f32, TokenType::Convert, Opcode::I32ReinterpretF32
i32.rem_s, TokenType::Binary, Opcode::I32RemS
i32.rem_u, TokenType::Binary, Opcode::I32RemU
i32.rotl, TokenType::Binary, Opcode::I32Rotl
i32.rotr, TokenType::Binary, Opcode::I32Rotr
i32.shl, TokenType::Binary, Opcode::I32Shl
i32.shr_s, TokenType::Binary, Opcode::I32ShrS
i32.shr_u, TokenType::Binary, Opcode::I32ShrU
i32.store16, TokenType::Store, Opcode::I32Store16
i32.store8, TokenType::Store, Opcode::I32Store8
i32.store, TokenType::Store, Opcode::I32Store
i32.sub, TokenType::Binary, Opcode::I32Sub
i32.trunc_f32_s, TokenType::Convert, Opcode::I32TruncF32S
i32.trunc_f32_u, TokenType::Convert, Opcode::I32TruncF32U
i32.trunc_f64_s, TokenType::Convert, Opcode::I32TruncF64S
i32.trunc_f64_u, TokenType::Convert, Opcode::I32TruncF64U
i32.trunc_sat_f32_s, TokenType::Convert, Opcode::I32TruncSatF32S
i32.trunc_sat_f32_u, TokenType::Convert, Opcode::I32TruncSatF32U
i32.trunc_sat_f64_s, TokenType::Convert, Opcode::I32TruncSatF64S
i32.trunc_sat_f64_u, TokenType::Convert, Opcode::I32TruncSatF64U
i32, Type::I32
i32.wrap_i64, TokenType::Convert, Opcode::I32WrapI64
i32x4.abs, TokenType::Unary, Opcode::I32X4Abs
i32x4.add, TokenType::Binary, Opcode::I32X4Add
i32x4.all_true, TokenType::Unary, Opcode::I32X4AllTrue
i32x4.bitmask, TokenType::Unary, Opcode::I32X4Bitmask
i32x4.eq, TokenType::Compare, Opcode::I32X4Eq
i32x4.extract_lane, TokenType::SimdLaneOp, Opcode::I32X4ExtractLane
i32x4.ge_s, TokenType::Compare, Opcode::I32X4GeS
i32x4.ge_u, TokenType::Compare, Opcode::I32X4GeU
i32x4.gt_s, TokenType::Compare, Opcode::I32X4GtS
i32x4.gt_u, TokenType::Compare, Opcode::I32X4GtU
i32x4.le_s, TokenType::Compare, Opcode::I32X4LeS
i32x4.le_u, TokenType::Compare, Opcode::I32X4LeU
v128.load16x4_s, TokenType::Load, Opcode::V128Load16X4S
v128.load16x4_u, TokenType::Load, Opcode::V128Load16X4U
i32x4.lt_s, TokenType::Compare, Opcode::I32X4LtS
i32x4.lt_u, TokenType::Compare, Opcode::I32X4LtU
i32x4.max_s, TokenType::Binary, Opcode::I32X4MaxS
i32x4.max_u, TokenType::Binary, Opcode::I32X4MaxU
i32x4.min_s, TokenType::Binary, Opcode::I32X4MinS
i32x4.min_u, TokenType::Binary, Opcode::I32X4MinU
i32x4.dot_i16x8_s, TokenType::Binary, Opcode::I32X4DotI16X8S
i32x4.mul, TokenType::Binary, Opcode::I32X4Mul
i32x4.neg, TokenType::Unary, Opcode::I32X4Neg
i32x4.ne, TokenType::Compare, Opcode::I32X4Ne
i32x4.replace_lane, TokenType::SimdLaneOp, Opcode::I32X4ReplaceLane
i32x4.shl, TokenType::Binary, Opcode::I32X4Shl
i32x4.shr_s, TokenType::Binary, Opcode::I32X4ShrS
i32x4.shr_u, TokenType::Binary, Opcode::I32X4ShrU
i32x4.splat, TokenType::Unary, Opcode::I32X4Splat
i32x4.sub, TokenType::Binary, Opcode::I32X4Sub
i32x4.extadd_pairwise_i16x8_s, TokenType::Unary, Opcode::I32X4ExtaddPairwiseI16X8S
i32x4.extadd_pairwise_i16x8_u, TokenType::Unary, Opcode::I32X4ExtaddPairwiseI16X8U
i32x4.extmul_low_i16x8_s, TokenType::Binary, Opcode::I32X4ExtmulLowI16X8S
i32x4.extmul_high_i16x8_s, TokenType::Binary, Opcode::I32X4ExtmulHighI16X8S
i32x4.extmul_low_i16x8_u, TokenType::Binary, Opcode::I32X4ExtmulLowI16X8U
i32x4.extmul_high_i16x8_u, TokenType::Binary, Opcode::I32X4ExtmulHighI16X8U
i32x4, TokenType::I32X4
i32x4.trunc_sat_f32x4_s, TokenType::Unary, Opcode::I32X4TruncSatF32X4S
i32x4.trunc_sat_f32x4_u, TokenType::Unary, Opcode::I32X4TruncSatF32X4U
i32x4.extend_high_i16x8_s, TokenType::Unary, Opcode::I32X4ExtendHighI16X8S
i32x4.extend_high_i16x8_u, TokenType::Unary, Opcode::I32X4ExtendHighI16X8U
i32x4.extend_low_i16x8_s, TokenType::Unary, Opcode::I32X4ExtendLowI16X8S
i32x4.extend_low_i16x8_u, TokenType::Unary, Opcode::I32X4ExtendLowI16X8U
i32x4.trunc_sat_f64x2_s_zero, TokenType::Unary, Opcode::I32X4TruncSatF64X2SZero
i32x4.trunc_sat_f64x2_u_zero, TokenType::Unary, Opcode::I32X4TruncSatF64X2UZero
i32.xor, TokenType::Binary, Opcode::I32Xor
i64.add, TokenType::Binary, Opcode::I64Add
i64.and, TokenType::Binary, Opcode::I64And
i64.atomic.load16_u, TokenType::AtomicLoad, Opcode::I64AtomicLoad16U
i64.atomic.load32_u, TokenType::AtomicLoad, Opcode::I64AtomicLoad32U
i64.atomic.load8_u, TokenType::AtomicLoad, Opcode::I64AtomicLoad8U
i64.atomic.load, TokenType::AtomicLoad, Opcode::I64AtomicLoad
i64.atomic.rmw16.add_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw16AddU
i64.atomic.rmw16.and_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw16AndU
i64.atomic.rmw16.cmpxchg_u, TokenType::AtomicRmwCmpxchg, Opcode::I64AtomicRmw16CmpxchgU
i64.atomic.rmw16.or_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw16OrU
i64.atomic.rmw16.sub_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw16SubU
i64.atomic.rmw16.xchg_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw16XchgU
i64.atomic.rmw16.xor_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw16XorU
i64.atomic.rmw32.add_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw32AddU
i64.atomic.rmw32.and_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw32AndU
i64.atomic.rmw32.cmpxchg_u, TokenType::AtomicRmwCmpxchg, Opcode::I64AtomicRmw32CmpxchgU
i64.atomic.rmw32.or_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw32OrU
i64.atomic.rmw32.sub_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw32SubU
i64.atomic.rmw32.xchg_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw32XchgU
i64.atomic.rmw32.xor_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw32XorU
i64.atomic.rmw8.add_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw8AddU
i64.atomic.rmw8.and_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw8AndU
i64.atomic.rmw8.cmpxchg_u, TokenType::AtomicRmwCmpxchg, Opcode::I64AtomicRmw8CmpxchgU
i64.atomic.rmw8.or_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw8OrU
i64.atomic.rmw8.sub_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw8SubU
i64.atomic.rmw8.xchg_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw8XchgU
i64.atomic.rmw8.xor_u, TokenType::AtomicRmw, Opcode::I64AtomicRmw8XorU
i64.atomic.rmw.add, TokenType::AtomicRmw, Opcode::I64AtomicRmwAdd
i64.atomic.rmw.and, TokenType::AtomicRmw, Opcode::I64AtomicRmwAnd
i64.atomic.rmw.cmpxchg, TokenType::AtomicRmwCmpxchg, Opcode::I64AtomicRmwCmpxchg
i64.atomic.rmw.or, TokenType::AtomicRmw, Opcode::I64AtomicRmwOr
i64.atomic.rmw.sub, TokenType::AtomicRmw, Opcode::I64AtomicRmwSub
i64.atomic.rmw.xchg, TokenType::AtomicRmw, Opcode::I64AtomicRmwXchg
i64.atomic.rmw.xor, TokenType::AtomicRmw, Opcode::I64AtomicRmwXor
i64.atomic.store16, TokenType::AtomicStore, Opcode::I64AtomicStore16
i64.atomic.store32, TokenType::AtomicStore, Opcode::I64AtomicStore32
i64.atomic.store8, TokenType::AtomicStore, Opcode::I64AtomicStore8
i64.atomic.store, TokenType::AtomicStore, Opcode::I64AtomicStore
i64.clz, TokenType::Unary, Opcode::I64Clz
i64.const, TokenType::Const, Opcode::I64Const
i64.ctz, TokenType::Unary, Opcode::I64Ctz
i64.div_s, TokenType::Binary, Opcode::I64DivS
i64.div_u, TokenType::Binary, Opcode::I64DivU
i64.eq, TokenType::Compare, Opcode::I64Eq
i64.eqz, TokenType::Convert, Opcode::I64Eqz
i64.extend16_s, TokenType::Unary, Opcode::I64Extend16S
i64.extend32_s, TokenType::Unary, Opcode::I64Extend32S
i64.extend8_s, TokenType::Unary, Opcode::I64Extend8S
i64.extend_i32_s, TokenType::Convert, Opcode::I64ExtendI32S
i64.extend_i32_u, TokenType::Convert, Opcode::I64ExtendI32U
i64.ge_s, TokenType::Compare, Opcode::I64GeS
i64.ge_u, TokenType::Compare, Opcode::I64GeU
i64.gt_s, TokenType::Compare, Opcode::I64GtS
i64.gt_u, TokenType::Compare, Opcode::I64GtU
i64.le_s, TokenType::Compare, Opcode::I64LeS
i64.le_u, TokenType::Compare, Opcode::I64LeU
i64.load16_s, TokenType::Load, Opcode::I64Load16S
i64.load16_u, TokenType::Load, Opcode::I64Load16U
i64.load32_s, TokenType::Load, Opcode::I64Load32S
i64.load32_u, TokenType::Load, Opcode::I64Load32U
i64.load8_s, TokenType::Load, Opcode::I64Load8S
i64.load8_u, TokenType::Load, Opcode::I64Load8U
i64.load, TokenType::Load, Opcode::I64Load
i64.lt_s, TokenType::Compare, Opcode::I64LtS
i64.lt_u, TokenType::Compare, Opcode::I64LtU
i64.mul, TokenType::Binary, Opcode::I64Mul
i64.ne, TokenType::Compare, Opcode::I64Ne
i64.or, TokenType::Binary, Opcode::I64Or
i64.popcnt, TokenType::Unary, Opcode::I64Popcnt
i64.reinterpret_f64, TokenType::Convert, Opcode::I64ReinterpretF64
i64.rem_s, TokenType::Binary, Opcode::I64RemS
i64.rem_u, TokenType::Binary, Opcode::I64RemU
i64.rotl, TokenType::Binary, Opcode::I64Rotl
i64.rotr, TokenType::Binary, Opcode::I64Rotr
i64.shl, TokenType::Binary, Opcode::I64Shl
i64.shr_s, TokenType::Binary, Opcode::I64ShrS
i64.shr_u, TokenType::Binary, Opcode::I64ShrU
i64.store16, TokenType::Store, Opcode::I64Store16
i64.store32, TokenType::Store, Opcode::I64Store32
i64.store8, TokenType::Store, Opcode::I64Store8
i64.store, TokenType::Store, Opcode::I64Store
i64.sub, TokenType::Binary, Opcode::I64Sub
i64.trunc_f32_s, TokenType::Convert, Opcode::I64TruncF32S
i64.trunc_f32_u, TokenType::Convert, Opcode::I64TruncF32U
i64.trunc_f64_s, TokenType::Convert, Opcode::I64TruncF64S
i64.trunc_f64_u, TokenType::Convert, Opcode::I64TruncF64U
i64.trunc_sat_f32_s, TokenType::Convert, Opcode::I64TruncSatF32S
i64.trunc_sat_f32_u, TokenType::Convert, Opcode::I64TruncSatF32U
i64.trunc_sat_f64_s, TokenType::Convert, Opcode::I64TruncSatF64S
i64.trunc_sat_f64_u, TokenType::Convert, Opcode::I64TruncSatF64U
i64, Type::I64
i64x2.add, TokenType::Binary, Opcode::I64X2Add
i64x2.extract_lane, TokenType::SimdLaneOp, Opcode::I64X2ExtractLane
v128.load32x2_s, TokenType::Load, Opcode::V128Load32X2S
v128.load32x2_u, TokenType::Load, Opcode::V128Load32X2U
i64x2.mul, TokenType::Binary, Opcode::I64X2Mul
i64x2.eq, TokenType::Binary, Opcode::I64X2Eq
i64x2.ne, TokenType::Binary, Opcode::I64X2Ne
i64x2.lt_s, TokenType::Binary, Opcode::I64X2LtS
i64x2.gt_s, TokenType::Binary, Opcode::I64X2GtS
i64x2.le_s, TokenType::Binary, Opcode::I64X2LeS
i64x2.ge_s, TokenType::Binary, Opcode::I64X2GeS
i64x2.abs, TokenType::Unary, Opcode::I64X2Abs
i64x2.neg, TokenType::Unary, Opcode::I64X2Neg
i64x2.all_true, TokenType::Unary, Opcode::I64X2AllTrue
i64x2.bitmask, TokenType::Unary, Opcode::I64X2Bitmask
i64x2.extend_low_i32x4_s, TokenType::Unary, Opcode::I64X2ExtendLowI32X4S
i64x2.extend_high_i32x4_s, TokenType::Unary, Opcode::I64X2ExtendHighI32X4S
i64x2.extend_low_i32x4_u, TokenType::Unary, Opcode::I64X2ExtendLowI32X4U
i64x2.extend_high_i32x4_u, TokenType::Unary, Opcode::I64X2ExtendHighI32X4U
i64x2.replace_lane, TokenType::SimdLaneOp, Opcode::I64X2ReplaceLane
i64x2.shl, TokenType::Binary, Opcode::I64X2Shl
i64x2.shr_s, TokenType::Binary, Opcode::I64X2ShrS
i64x2.shr_u, TokenType::Binary, Opcode::I64X2ShrU
i64x2.splat, TokenType::Unary, Opcode::I64X2Splat
i64x2.sub, TokenType::Binary, Opcode::I64X2Sub
i64x2.extmul_low_i32x4_s, TokenType::Binary, Opcode::I64X2ExtmulLowI32X4S
i64x2.extmul_high_i32x4_s, TokenType::Binary, Opcode::I64X2ExtmulHighI32X4S
i64x2.extmul_low_i32x4_u, TokenType::Binary, Opcode::I64X2ExtmulLowI32X4U
i64x2.extmul_high_i32x4_u, TokenType::Binary, Opcode::I64X2ExtmulHighI32X4U
i64x2, TokenType::I64X2
i64.xor, TokenType::Binary, Opcode::I64Xor
i8x16.abs, TokenType::Unary, Opcode::I8X16Abs
i8x16.add_sat_s, TokenType::Binary, Opcode::I8X16AddSatS
i8x16.add_sat_u, TokenType::Binary, Opcode::I8X16AddSatU
i8x16.add, TokenType::Binary, Opcode::I8X16Add
i8x16.all_true, TokenType::Unary, Opcode::I8X16AllTrue
i8x16.avgr_u, TokenType::Binary, Opcode::I8X16AvgrU
i8x16.bitmask, TokenType::Unary, Opcode::I8X16Bitmask
i8x16.eq, TokenType::Compare, Opcode::I8X16Eq
i8x16.extract_lane_s, TokenType::SimdLaneOp, Opcode::I8X16ExtractLaneS
i8x16.extract_lane_u, TokenType::SimdLaneOp, Opcode::I8X16ExtractLaneU
i8x16.ge_s, TokenType::Compare, Opcode::I8X16GeS
i8x16.ge_u, TokenType::Compare, Opcode::I8X16GeU
i8x16.gt_s, TokenType::Compare, Opcode::I8X16GtS
i8x16.gt_u, TokenType::Compare, Opcode::I8X16GtU
i8x16.le_s, TokenType::Compare, Opcode::I8X16LeS
i8x16.le_u, TokenType::Compare, Opcode::I8X16LeU
i8x16.lt_s, TokenType::Compare, Opcode::I8X16LtS
i8x16.lt_u, TokenType::Compare, Opcode::I8X16LtU
i8x16.max_s, TokenType::Binary, Opcode::I8X16MaxS
i8x16.max_u, TokenType::Binary, Opcode::I8X16MaxU
i8x16.min_s, TokenType::Binary, Opcode::I8X16MinS
i8x16.min_u, TokenType::Binary, Opcode::I8X16MinU
i8x16.narrow_i16x8_s, TokenType::Binary, Opcode::I8X16NarrowI16X8S
i8x16.narrow_i16x8_u, TokenType::Binary, Opcode::I8X16NarrowI16X8U
i8x16.neg, TokenType::Unary, Opcode::I8X16Neg
i8x16.popcnt, TokenType::Unary, Opcode::I8X16Popcnt
i8x16.ne, TokenType::Compare, Opcode::I8X16Ne
i8x16.replace_lane, TokenType::SimdLaneOp, Opcode::I8X16ReplaceLane
i8x16.shl, TokenType::Binary, Opcode::I8X16Shl
i8x16.shr_s, TokenType::Binary, Opcode::I8X16ShrS
i8x16.shr_u, TokenType::Binary, Opcode::I8X16ShrU
i8x16.splat, TokenType::Unary, Opcode::I8X16Splat
i8x16.sub_sat_s, TokenType::Binary, Opcode::I8X16SubSatS
i8x16.sub_sat_u, TokenType::Binary, Opcode::I8X16SubSatU
i8x16.sub, TokenType::Binary, Opcode::I8X16Sub
i8x16, TokenType::I8X16
if, TokenType::If, Opcode::If
import, TokenType::Import
input, TokenType::Input
invoke, TokenType::Invoke
item, TokenType::Item
local.get, TokenType::LocalGet, Opcode::LocalGet
local.set, TokenType::LocalSet, Opcode::LocalSet
local.tee, TokenType::LocalTee, Opcode::LocalTee
local, TokenType::Local
loop, TokenType::Loop, Opcode::Loop
memory.atomic.notify, TokenType::AtomicNotify, Opcode::MemoryAtomicNotify
memory.atomic.wait32, TokenType::AtomicWait, Opcode::MemoryAtomicWait32
memory.atomic.wait64, TokenType::AtomicWait, Opcode::MemoryAtomicWait64
memory.copy, TokenType::MemoryCopy, Opcode::MemoryCopy
memory.fill, TokenType::MemoryFill, Opcode::MemoryFill
memory.grow, TokenType::MemoryGrow, Opcode::MemoryGrow
memory.init, TokenType::MemoryInit, Opcode::MemoryInit
memory.size, TokenType::MemorySize, Opcode::MemorySize
memory, TokenType::Memory
module, TokenType::Module
mut, TokenType::Mut
nan:arithmetic, TokenType::NanArithmetic
nan:canonical, TokenType::NanCanonical
nop, TokenType::Nop, Opcode::Nop
offset, TokenType::Offset
output, TokenType::Output
param, TokenType::Param
ref, TokenType::Ref
quote, TokenType::Quote
ref.extern, TokenType::RefExtern
ref.func, TokenType::RefFunc, Opcode::RefFunc
ref.is_null, TokenType::RefIsNull, Opcode::RefIsNull
ref.null, TokenType::RefNull, Opcode::RefNull
register, TokenType::Register
result, TokenType::Result
rethrow, TokenType::Rethrow, Opcode::Rethrow
return_call_indirect, TokenType::ReturnCallIndirect, Opcode::ReturnCallIndirect
return_call, TokenType::ReturnCall, Opcode::ReturnCall
return, TokenType::Return, Opcode::Return
select, TokenType::Select, Opcode::Select
shared, TokenType::Shared
start, TokenType::Start
struct, Type::Struct, TokenType::Struct
table.copy, TokenType::TableCopy, Opcode::TableCopy
table.fill, TokenType::TableFill, Opcode::TableFill
table.get, TokenType::TableGet, Opcode::TableGet
table.grow, TokenType::TableGrow, Opcode::TableGrow
table.init, TokenType::TableInit, Opcode::TableInit
table.set, TokenType::TableSet, Opcode::TableSet
table.size, TokenType::TableSize, Opcode::TableSize
table, TokenType::Table
then, TokenType::Then
throw, TokenType::Throw, Opcode::Throw
try, TokenType::Try, Opcode::Try
type, TokenType::Type
unreachable, TokenType::Unreachable, Opcode::Unreachable
v128.andnot, TokenType::Binary, Opcode::V128Andnot
v128.and, TokenType::Binary, Opcode::V128And
v128.bitselect, TokenType::Ternary, Opcode::V128BitSelect
v128.const, TokenType::Const, Opcode::V128Const
v128.load, TokenType::Load, Opcode::V128Load
v128.not, TokenType::Unary, Opcode::V128Not
v128.or, TokenType::Binary, Opcode::V128Or
v128.any_true, TokenType::Unary, Opcode::V128AnyTrue
v128.load32_zero, TokenType::Load, Opcode::V128Load32Zero
v128.load64_zero, TokenType::Load, Opcode::V128Load64Zero
v128.store, TokenType::Store, Opcode::V128Store
v128, Type::V128
v128.xor, TokenType::Binary, Opcode::V128Xor
v128.load16_splat, TokenType::Load, Opcode::V128Load16Splat
v128.load32_splat, TokenType::Load, Opcode::V128Load32Splat
v128.load64_splat, TokenType::Load, Opcode::V128Load64Splat
v128.load8_splat, TokenType::Load, Opcode::V128Load8Splat
v128.load8_lane, TokenType::SimdLoadLane, Opcode::V128Load8Lane
v128.load16_lane, TokenType::SimdLoadLane, Opcode::V128Load16Lane
v128.load32_lane, TokenType::SimdLoadLane, Opcode::V128Load32Lane
v128.load64_lane, TokenType::SimdLoadLane, Opcode::V128Load64Lane
v128.store8_lane, TokenType::SimdStoreLane, Opcode::V128Store8Lane
v128.store16_lane, TokenType::SimdStoreLane, Opcode::V128Store16Lane
v128.store32_lane, TokenType::SimdStoreLane, Opcode::V128Store32Lane
v128.store64_lane, TokenType::SimdStoreLane, Opcode::V128Store64Lane
i8x16.shuffle, TokenType::SimdShuffleOp, Opcode::I8X16Shuffle
i8x16.swizzle, TokenType::Binary, Opcode::I8X16Swizzle
