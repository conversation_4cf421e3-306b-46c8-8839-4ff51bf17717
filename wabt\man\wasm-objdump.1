.Dd $Mdocdate$
.Dt WABT 1
.Os
.Sh NAME
.Nm wasm-objdump
.Nd print information about a wasm binary
.Sh SYNOPSIS
.Nm wasm-objdump
.Op options
.Ar
.Sh DESCRIPTION
.Nm
prints information about a wasm binary, similar to objdump.
.Pp
The options are as follows:
.Bl -tag -width Ds
.It Fl h , Fl Fl headers
Print headers
.It Fl j , Fl Fl section=SECTION
Select just one section
.It Fl s , Fl Fl full-contents
Print raw section contents
.It Fl d , Fl Fl disassemble
Disassemble function bodies
.It Fl Fl debug
Print extra debug information
.It Fl x , Fl Fl details
Show section details
.It Fl r , Fl Fl reloc
Show relocations inline with disassembly
.It Fl Fl help
Print a help message
.El
.Sh EXAMPLES
.Dl $ wasm-objdump test.wasm
.Sh SEE ALSO
.Xr wasm-interp 1 ,
.Xr wasm-opcodecnt 1 ,
.Xr wasm-strip 1 ,
.Xr wasm-validate 1 ,
.Xr wasm2c 1 ,
.Xr wasm2wat 1 ,
.Xr wast2json 1 ,
.Xr wat-desugar 1 ,
.Xr wat2wasm 1 ,
.Xr spectest-interp 1
.Sh BUGS
If you find a bug, please report it at
.br
.Lk https://github.com/WebAssembly/wabt/issues .
