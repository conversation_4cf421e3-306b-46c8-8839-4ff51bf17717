.Dd $Mdocdate$
.Dt WABT 1
.Os
.Sh NAME
.Nm wast2json
.Nd convert a file in the wasm spec test format to a JSON file and associated wasm binary files
.Sh SYNOPSIS
.Nm wast2json
.Op options
.Ar file
.Sh DESCRIPTION
.Nm
reads a file in the wasm spec test format, checks it for errors, and converts it to a JSON file and associated wasm binary files.
.Pp
The options are as follows:
.Bl -tag -width Ds
.It Fl v , Fl Fl verbose
Use multiple times for more info
.It Fl Fl help
Print this help message
.It Fl Fl debug-parser
Turn on debugging the parser of wast files
.It Fl Fl enable-exceptions
Enable Experimental exception handling
.It Fl Fl disable-mutable-globals
Disable Import/export mutable globals
.It Fl Fl enable-saturating-float-to-int
Enable Saturating float-to-int operators
.It Fl Fl enable-sign-extension
Enable Sign-extension operators
.It Fl Fl disable-simd
Disable SIMD support
.It Fl Fl enable-threads
Enable Threading support
.It Fl Fl enable-multi-value
Enable Multi-value
.It Fl Fl enable-tail-call
Enable Tail-call support
.It Fl o , Fl Fl output=FILE
output wasm binary file
.It Fl r , Fl Fl relocatable
Create a relocatable wasm binary (suitable for linking with e.g. lld)
.It Fl Fl no-canonicalize-leb128s
Write all LEB128 sizes as 5-bytes instead of their minimal size
.It Fl Fl debug-names
Write debug names to the generated binary file
.It Fl Fl no-check
Don't check for invalid modules
.El
.Sh EXAMPLES
Parse spec-test.wast, and write files to spec-test.json.
Modules are written to spec-test.0.wasm, spec-test.1.wasm, etc.
.Pp
.Dl $ wast2json spec-test.wast -o spec-test.json
.Sh SEE ALSO
.Xr wasm-interp 1 ,
.Xr wasm-objdump 1 ,
.Xr wasm-opcodecnt 1 ,
.Xr wasm-strip 1 ,
.Xr wasm-validate 1 ,
.Xr wasm2c 1 ,
.Xr wasm2wat 1 ,
.Xr wat-desugar 1 ,
.Xr wat2wasm 1 ,
.Xr spectest-interp 1
.Sh BUGS
If you find a bug, please report it at
.br
.Lk https://github.com/WebAssembly/wabt/issues .
