/*
 * Copyright 2018 WebAssembly Community Group participants
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef WABT_TOKEN
#error "You must define WABT_TOKEN before including this file."
#endif

/* Tokens with no additional data (i.e. bare). */
WABT_TOKEN(Invalid, "Invalid")
WABT_TOKEN(Array, "array")
WABT_TOKEN(AssertException, "assert_exception")
WABT_TOKEN(AssertExhaustion, "assert_exhaustion")
WABT_TOKEN(AssertInvalid, "assert_invalid")
WABT_TOKEN(AssertMalformed, "assert_malformed")
WABT_TOKEN(AssertReturn, "assert_return")
WABT_TOKEN(AssertTrap, "assert_trap")
WABT_TOKEN(AssertUnlinkable, "assert_unlinkable")
WABT_TOKEN(Bin, "bin")
WABT_TOKEN(Item, "item")
WABT_TOKEN(Data, "data")
WABT_TOKEN(Declare, "declare")
WABT_TOKEN(Delegate, "delegate")
WABT_TOKEN(Do, "do")
WABT_TOKEN(Elem, "elem")
WABT_TOKEN(Eof, "EOF")
WABT_TOKEN(Tag, "tag")
WABT_TOKEN(Export, "export")
WABT_TOKEN(Field, "field")
WABT_TOKEN(Get, "get")
WABT_TOKEN(Global, "global")
WABT_TOKEN(Import, "import")
WABT_TOKEN(Invoke, "invoke")
WABT_TOKEN(Input, "input")
WABT_TOKEN(Local, "local")
WABT_TOKEN(Lpar, "(")
WABT_TOKEN(Memory, "memory")
WABT_TOKEN(Module, "module")
WABT_TOKEN(Mut, "mut")
WABT_TOKEN(NanArithmetic, "nan:arithmetic")
WABT_TOKEN(NanCanonical, "nan:canonical")
WABT_TOKEN(Offset, "offset")
WABT_TOKEN(Output, "output")
WABT_TOKEN(Param, "param")
WABT_TOKEN(Ref, "ref")
WABT_TOKEN(Quote, "quote")
WABT_TOKEN(Register, "register")
WABT_TOKEN(Result, "result")
WABT_TOKEN(Rpar, ")")
WABT_TOKEN(Shared, "shared")
WABT_TOKEN(Start, "start")
WABT_TOKEN(Struct, "struct")
WABT_TOKEN(Table, "table")
WABT_TOKEN(Then, "then")
WABT_TOKEN(Type, "type")
WABT_TOKEN(I8X16, "i8x16")
WABT_TOKEN(I16X8, "i16x8")
WABT_TOKEN(I32X4, "i32x4")
WABT_TOKEN(I64X2, "i64x2")
WABT_TOKEN(F32X4, "f32x4")
WABT_TOKEN(F64X2, "f64x2")
WABT_TOKEN_FIRST(Bare, Invalid)
WABT_TOKEN_LAST(Bare, F64X2)

/* Tokens with Literal data. */
WABT_TOKEN(Float, "FLOAT")
WABT_TOKEN(Int, "INT")
WABT_TOKEN(Nat, "NAT")
WABT_TOKEN_FIRST(Literal, Float)
WABT_TOKEN_LAST(Literal, Nat)

/* Tokens with Opcode data. */
WABT_TOKEN(AtomicFence, "atomic.fence")
WABT_TOKEN(AtomicLoad, "ATOMIC_LOAD")
WABT_TOKEN(AtomicNotify, "ATOMIC_NOTIFY")
WABT_TOKEN(AtomicRmw, "ATOMIC_RMW")
WABT_TOKEN(AtomicRmwCmpxchg, "ATOMIC_RMW_CMPXCHG")
WABT_TOKEN(AtomicStore, "ATOMIC_STORE")
WABT_TOKEN(AtomicWait, "ATOMIC_WAIT")
WABT_TOKEN(Binary, "BINARY")
WABT_TOKEN(Block, "block")
WABT_TOKEN(Br, "br")
WABT_TOKEN(BrIf, "br_if")
WABT_TOKEN(BrTable, "br_table")
WABT_TOKEN(Call, "call")
WABT_TOKEN(CallIndirect, "call_indirect")
WABT_TOKEN(CallRef, "call_ref")
WABT_TOKEN(Catch, "catch")
WABT_TOKEN(CatchAll, "catch_all")
WABT_TOKEN(Compare, "COMPARE")
WABT_TOKEN(Const, "CONST")
WABT_TOKEN(Convert, "CONVERT")
WABT_TOKEN(DataDrop, "data.drop")
WABT_TOKEN(Drop, "drop")
WABT_TOKEN(ElemDrop, "elem.drop")
WABT_TOKEN(Else, "else")
WABT_TOKEN(End, "end")
WABT_TOKEN(GlobalGet, "global.get")
WABT_TOKEN(GlobalSet, "global.set")
WABT_TOKEN(If, "if")
WABT_TOKEN(Load, "LOAD")
WABT_TOKEN(LocalGet, "local.get")
WABT_TOKEN(LocalSet, "local.set")
WABT_TOKEN(LocalTee, "local.tee")
WABT_TOKEN(Loop, "loop")
WABT_TOKEN(MemoryCopy, "memory.copy")
WABT_TOKEN(MemoryFill, "memory.fill")
WABT_TOKEN(MemoryGrow, "memory.grow")
WABT_TOKEN(MemoryInit, "memory.init")
WABT_TOKEN(MemorySize, "memory.size")
WABT_TOKEN(Nop, "nop")
WABT_TOKEN(RefExtern, "ref.extern")
WABT_TOKEN(RefFunc, "ref.func")
WABT_TOKEN(RefIsNull, "ref.is_null")
WABT_TOKEN(RefNull, "ref.null")
WABT_TOKEN(Rethrow, "rethrow")
WABT_TOKEN(ReturnCallIndirect, "return_call_indirect")
WABT_TOKEN(ReturnCall, "return_call")
WABT_TOKEN(Return, "return")
WABT_TOKEN(Select, "select")
WABT_TOKEN(SimdLaneOp, "SIMDLANEOP")
WABT_TOKEN(SimdLoadSplat, "SIMDLOADSPLAT")
WABT_TOKEN(SimdLoadLane, "SIMDLOADLANE")
WABT_TOKEN(SimdStoreLane, "SIMDSTORELANE")
WABT_TOKEN(SimdShuffleOp, "i8x16.shuffle")
WABT_TOKEN(Store, "STORE")
WABT_TOKEN(TableCopy, "table.copy")
WABT_TOKEN(TableFill, "table.full")
WABT_TOKEN(TableGet, "table.get")
WABT_TOKEN(TableGrow, "table.grow")
WABT_TOKEN(TableInit, "table.init")
WABT_TOKEN(TableSet, "table.set")
WABT_TOKEN(TableSize, "table.size")
WABT_TOKEN(Ternary, "TERNARY")
WABT_TOKEN(Throw, "throw")
WABT_TOKEN(Try, "try")
WABT_TOKEN(Unary, "UNARY")
WABT_TOKEN(Unreachable, "unreachable")
WABT_TOKEN_FIRST(Opcode, AtomicFence)
WABT_TOKEN_LAST(Opcode, Unreachable)

/* Tokens with string data. */
WABT_TOKEN(AlignEqNat, "align=")
WABT_TOKEN(LparAnn, "Annotation")
WABT_TOKEN(OffsetEqNat, "offset=")
WABT_TOKEN(Reserved, "Reserved")
WABT_TOKEN(Text, "TEXT")
WABT_TOKEN(Var, "VAR")
WABT_TOKEN_FIRST(String, AlignEqNat)
WABT_TOKEN_LAST(String, Var)

/* Tokens with Type data. */
WABT_TOKEN(ValueType, "VALUETYPE")
WABT_TOKEN_FIRST(Type, ValueType)
WABT_TOKEN_LAST(Type, ValueType)

/* Tokens with Type data, but are reference kinds. */
WABT_TOKEN(Func, "func")
WABT_TOKEN(Extern, "extern")
WABT_TOKEN(Exn, "exn")
WABT_TOKEN_FIRST(RefKind, Func)
WABT_TOKEN_LAST(RefKind, Exn)
