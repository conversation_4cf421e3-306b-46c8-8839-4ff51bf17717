;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (func (result i32)
    block (result i32)
      i32.const 1
      br 0 
    end))

(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 01                                        ; num results
000000e: 7f                                        ; i32
0000009: 05                                        ; FIXUP section size
; section "Function" (3)
000000f: 03                                        ; section code
0000010: 00                                        ; section size (guess)
0000011: 01                                        ; num functions
0000012: 00                                        ; function 0 signature index
0000010: 02                                        ; FIXUP section size
; section "Code" (10)
0000013: 0a                                        ; section code
0000014: 00                                        ; section size (guess)
0000015: 01                                        ; num functions
; function body 0
0000016: 00                                        ; func body size (guess)
0000017: 00                                        ; local decl count
0000018: 02                                        ; block
0000019: 7f                                        ; i32
000001a: 41                                        ; i32.const
000001b: 01                                        ; i32 literal
000001c: 0c                                        ; br
000001d: 00                                        ; break depth
000001e: 0b                                        ; end
000001f: 0b                                        ; end
0000016: 09                                        ; FIXUP func body size
0000014: 0b                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

expr-br.wasm:	file format wasm 0x1

Code Disassembly:

000017 func[0]:
 000018: 02 7f                      | block i32
 00001a: 41 01                      |   i32.const 1
 00001c: 0c 00                      |   br 0
 00001e: 0b                         | end
 00001f: 0b                         | end
;;; STDOUT ;;)
