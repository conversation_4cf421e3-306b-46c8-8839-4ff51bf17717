;;; TOOL: run-objdump-gen-wasm
;;; ARGS: -x
magic
version
section("dylink") {
  mem_size[5]
  mem_align[1]
  table_size[3]
  table_align[2]
  needed_count[2]
  str("libfoo.so")
  str("libbar.so")
}
(;; STDOUT ;;;

dylink-section.wasm:	file format wasm 0x1

Section Details:

Custom:
 - name: "dylink"
 - mem_size     : 5
 - mem_p2align  : 1
 - table_size   : 3
 - table_p2align: 2
 - needed_dynlibs[2]:
  - libfoo.so
  - libbar.so

Code Disassembly:

;;; STDOUT ;;)
