;;; TOOL: run-gen-wasm-bad
;; names section out of order (1 first, then 0) which is an error.
magic
version
section(TYPE) { count[1] function params[0] results[1] i32 }
section(FUNCTION) { count[2] type[0] type[0] }
section(CODE) {
  count[2]
  func { locals[decl_count[0]] }
  func { locals[decl_count[0]] }
}
section("name") {
  section(NAME_FUNCTION) {
    func_count[2]
    index[1]
    str("F1")
    index[0]
    str("F0")
  }
}
(;; STDERR ;;;
000002c: error: function index out of order: 0
000002c: error: function index out of order: 0
;;; STDERR ;;)
