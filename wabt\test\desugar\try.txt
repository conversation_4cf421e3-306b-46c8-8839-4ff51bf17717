;;; TOOL: wat-desugar
;;; ARGS: --enable-exceptions
(module
  (tag $ex (param i32))
  (func (result i32)
    try $try1 (result i32)
      nop
      i32.const 7
    catch $ex
      nop
    end
  )
)
(;; STDOUT ;;;
(module
  (tag $ex (param i32))
  (func (;0;) (result i32)
    try $try1 (result i32)
      nop
      i32.const 7
    catch $ex
      nop
    end)
  (type (;0;) (func (param i32)))
  (type (;1;) (func (result i32))))
;;; STDOUT ;;)
