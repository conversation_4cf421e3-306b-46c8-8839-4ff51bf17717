;;; TOOL: run-objdump

(module
  ;; i8x16 shl
  (func (export "i8x16_shl_0") (result v128)
    v128.const i32x4 0xff000001 0xe0000002 0x00000003 0x00000004
    i32.const 3
    i8x16.shl)

  ;; i16x8 shl
  (func (export "i16x8_shl_0") (result v128)
    v128.const i32x4 0xff000071 0xe0000702 0x00000003 0x00000004
    i32.const 3
    i16x8.shl)

  ;; i32x4 shl
  (func (export "i32x4_shl_0") (result v128)
    v128.const i32x4 0xff0ff071 0xe0077702 0xe0004003 0x00002004
    i32.const 3
    i32x4.shl)

  ;; i64x2 shl
  (func (export "i64x2_shl_0") (result v128)
    v128.const i32x4 0xff000055 0xe0000702 0xe0004003 0x00002004
    i32.const 3
    i64x2.shl)

  ;; i8x16 shr (signed and unsigned)
  (func (export "i8x16_shr_s_0") (result v128)
    v128.const i32x4 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
    i32.const 3
    i8x16.shr_s)
  (func (export "i8x16_shr_u_0") (result v128)
    v128.const i32x4 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
    i32.const 3
    i8x16.shr_u)

  ;; i16x8 shr (signed and unsigned)
  (func (export "i16x8_shr_s_0") (result v128)
    v128.const i32x4 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
    i32.const 3
    i16x8.shr_s)
  (func (export "i16x8_shr_u_0") (result v128)
    v128.const i32x4 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
    i32.const 3
    i16x8.shr_u)

  ;; i32x4 shr (signed and unsigned)
  (func (export "i32x4_shr_s_0") (result v128)
    v128.const i32x4 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
    i32.const 3
    i32x4.shr_s)
  (func (export "i32x4_shr_u_0") (result v128)
    v128.const i32x4 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
    i32.const 3
    i32x4.shr_u)

  ;; i64x2 shr (signed and unsigned)
  (func (export "i64x2_shr_s_0") (result v128)
    v128.const i32x4 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
    i32.const 3
    i64x2.shr_s)
  (func (export "i64x2_shr_u_0") (result v128)
    v128.const i32x4 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
    i32.const 3
    i64x2.shr_u)
  )
(;; STDOUT ;;;

simd-shift.wasm:	file format wasm 0x1

Code Disassembly:

0000df func[0] <i8x16_shl_0>:
 0000e0: fd 0c 01 00 00 ff 02 00 00 | v128.const 0xff000001 0xe0000002 0x00000003 0x00000004
 0000e9: e0 03 00 00 00 04 00 00 00 | 
 0000f2: 41 03                      | i32.const 3
 0000f4: fd 6b                      | i8x16.shl
 0000f6: 0b                         | end
0000f8 func[1] <i16x8_shl_0>:
 0000f9: fd 0c 71 00 00 ff 02 07 00 | v128.const 0xff000071 0xe0000702 0x00000003 0x00000004
 000102: e0 03 00 00 00 04 00 00 00 | 
 00010b: 41 03                      | i32.const 3
 00010d: fd 8b 01                   | i16x8.shl
 000110: 0b                         | end
000112 func[2] <i32x4_shl_0>:
 000113: fd 0c 71 f0 0f ff 02 77 07 | v128.const 0xff0ff071 0xe0077702 0xe0004003 0x00002004
 00011c: e0 03 40 00 e0 04 20 00 00 | 
 000125: 41 03                      | i32.const 3
 000127: fd ab 01                   | i32x4.shl
 00012a: 0b                         | end
00012c func[3] <i64x2_shl_0>:
 00012d: fd 0c 55 00 00 ff 02 07 00 | v128.const 0xff000055 0xe0000702 0xe0004003 0x00002004
 000136: e0 03 40 00 e0 04 20 00 00 | 
 00013f: 41 03                      | i32.const 3
 000141: fd cb 01                   | i64x2.shl
 000144: 0b                         | end
000146 func[4] <i8x16_shr_s_0>:
 000147: fd 0c 0f 00 00 ff 02 70 0f | v128.const 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
 000150: e0 03 00 00 0f 04 f0 0f 00 | 
 000159: 41 03                      | i32.const 3
 00015b: fd 6c                      | i8x16.shr_s
 00015d: 0b                         | end
00015f func[5] <i8x16_shr_u_0>:
 000160: fd 0c 0f 00 00 ff 02 70 0f | v128.const 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
 000169: e0 03 00 00 0f 04 f0 0f 00 | 
 000172: 41 03                      | i32.const 3
 000174: fd 6d                      | i8x16.shr_u
 000176: 0b                         | end
000178 func[6] <i16x8_shr_s_0>:
 000179: fd 0c 0f 00 00 ff 02 70 0f | v128.const 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
 000182: e0 03 00 00 0f 04 f0 0f 00 | 
 00018b: 41 03                      | i32.const 3
 00018d: fd 8c 01                   | i16x8.shr_s
 000190: 0b                         | end
000192 func[7] <i16x8_shr_u_0>:
 000193: fd 0c 0f 00 00 ff 02 70 0f | v128.const 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
 00019c: e0 03 00 00 0f 04 f0 0f 00 | 
 0001a5: 41 03                      | i32.const 3
 0001a7: fd 8d 01                   | i16x8.shr_u
 0001aa: 0b                         | end
0001ac func[8] <i32x4_shr_s_0>:
 0001ad: fd 0c 0f 00 00 ff 02 70 0f | v128.const 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
 0001b6: e0 03 00 00 0f 04 f0 0f 00 | 
 0001bf: 41 03                      | i32.const 3
 0001c1: fd ac 01                   | i32x4.shr_s
 0001c4: 0b                         | end
0001c6 func[9] <i32x4_shr_u_0>:
 0001c7: fd 0c 0f 00 00 ff 02 70 0f | v128.const 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
 0001d0: e0 03 00 00 0f 04 f0 0f 00 | 
 0001d9: 41 03                      | i32.const 3
 0001db: fd ad 01                   | i32x4.shr_u
 0001de: 0b                         | end
0001e0 func[10] <i64x2_shr_s_0>:
 0001e1: fd 0c 0f 00 00 ff 02 70 0f | v128.const 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
 0001ea: e0 03 00 00 0f 04 f0 0f 00 | 
 0001f3: 41 03                      | i32.const 3
 0001f5: fd cc 01                   | i64x2.shr_s
 0001f8: 0b                         | end
0001fa func[11] <i64x2_shr_u_0>:
 0001fb: fd 0c 0f 00 00 ff 02 70 0f | v128.const 0xff00000f 0xe00f7002 0x0f000003 0x000ff004
 000204: e0 03 00 00 0f 04 f0 0f 00 | 
 00020d: 41 03                      | i32.const 3
 00020f: fd cd 01                   | i64x2.shr_u
 000212: 0b                         | end
;;; STDOUT ;;)
