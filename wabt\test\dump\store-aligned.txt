;;; TOOL: run-objdump
;;; ARGS0: -v
(module
  (memory 1)
  (func
    i32.const 0
    i32.const 0
    i32.store8 align=1
    i32.const 0
    i32.const 0
    i32.store16 align=1
    i32.const 0
    i32.const 0
    i32.store16 align=2
    i32.const 0
    i32.const 0
    i32.store align=1
    i32.const 0
    i32.const 0
    i32.store align=2
    i32.const 0
    i32.const 0
    i32.store align=4
    i32.const 0
    i64.const 0
    i64.store8 align=1
    i32.const 0
    i64.const 0
    i64.store16 align=1
    i32.const 0
    i64.const 0
    i64.store16 align=2
    i32.const 0
    i64.const 0
    i64.store32 align=1
    i32.const 0
    i64.const 0
    i64.store32 align=2
    i32.const 0
    i64.const 0
    i64.store32 align=4
    i32.const 0
    i64.const 0
    i64.store align=1
    i32.const 0
    i64.const 0
    i64.store align=2
    i32.const 0
    i64.const 0
    i64.store align=4
    i32.const 0
    i64.const 0
    i64.store align=8))
(;; STDERR ;;;
0000000: 0061 736d                                 ; WASM_BINARY_MAGIC
0000004: 0100 0000                                 ; WASM_BINARY_VERSION
; section "Type" (1)
0000008: 01                                        ; section code
0000009: 00                                        ; section size (guess)
000000a: 01                                        ; num types
; func type 0
000000b: 60                                        ; func
000000c: 00                                        ; num params
000000d: 00                                        ; num results
0000009: 04                                        ; FIXUP section size
; section "Function" (3)
000000e: 03                                        ; section code
000000f: 00                                        ; section size (guess)
0000010: 01                                        ; num functions
0000011: 00                                        ; function 0 signature index
000000f: 02                                        ; FIXUP section size
; section "Memory" (5)
0000012: 05                                        ; section code
0000013: 00                                        ; section size (guess)
0000014: 01                                        ; num memories
; memory 0
0000015: 00                                        ; limits: flags
0000016: 01                                        ; limits: initial
0000013: 03                                        ; FIXUP section size
; section "Code" (10)
0000017: 0a                                        ; section code
0000018: 00                                        ; section size (guess)
0000019: 01                                        ; num functions
; function body 0
000001a: 00                                        ; func body size (guess)
000001b: 00                                        ; local decl count
000001c: 41                                        ; i32.const
000001d: 00                                        ; i32 literal
000001e: 41                                        ; i32.const
000001f: 00                                        ; i32 literal
0000020: 3a                                        ; i32.store8
0000021: 00                                        ; alignment
0000022: 00                                        ; store offset
0000023: 41                                        ; i32.const
0000024: 00                                        ; i32 literal
0000025: 41                                        ; i32.const
0000026: 00                                        ; i32 literal
0000027: 3b                                        ; i32.store16
0000028: 00                                        ; alignment
0000029: 00                                        ; store offset
000002a: 41                                        ; i32.const
000002b: 00                                        ; i32 literal
000002c: 41                                        ; i32.const
000002d: 00                                        ; i32 literal
000002e: 3b                                        ; i32.store16
000002f: 01                                        ; alignment
0000030: 00                                        ; store offset
0000031: 41                                        ; i32.const
0000032: 00                                        ; i32 literal
0000033: 41                                        ; i32.const
0000034: 00                                        ; i32 literal
0000035: 36                                        ; i32.store
0000036: 00                                        ; alignment
0000037: 00                                        ; store offset
0000038: 41                                        ; i32.const
0000039: 00                                        ; i32 literal
000003a: 41                                        ; i32.const
000003b: 00                                        ; i32 literal
000003c: 36                                        ; i32.store
000003d: 01                                        ; alignment
000003e: 00                                        ; store offset
000003f: 41                                        ; i32.const
0000040: 00                                        ; i32 literal
0000041: 41                                        ; i32.const
0000042: 00                                        ; i32 literal
0000043: 36                                        ; i32.store
0000044: 02                                        ; alignment
0000045: 00                                        ; store offset
0000046: 41                                        ; i32.const
0000047: 00                                        ; i32 literal
0000048: 42                                        ; i64.const
0000049: 00                                        ; i64 literal
000004a: 3c                                        ; i64.store8
000004b: 00                                        ; alignment
000004c: 00                                        ; store offset
000004d: 41                                        ; i32.const
000004e: 00                                        ; i32 literal
000004f: 42                                        ; i64.const
0000050: 00                                        ; i64 literal
0000051: 3d                                        ; i64.store16
0000052: 00                                        ; alignment
0000053: 00                                        ; store offset
0000054: 41                                        ; i32.const
0000055: 00                                        ; i32 literal
0000056: 42                                        ; i64.const
0000057: 00                                        ; i64 literal
0000058: 3d                                        ; i64.store16
0000059: 01                                        ; alignment
000005a: 00                                        ; store offset
000005b: 41                                        ; i32.const
000005c: 00                                        ; i32 literal
000005d: 42                                        ; i64.const
000005e: 00                                        ; i64 literal
000005f: 3e                                        ; i64.store32
0000060: 00                                        ; alignment
0000061: 00                                        ; store offset
0000062: 41                                        ; i32.const
0000063: 00                                        ; i32 literal
0000064: 42                                        ; i64.const
0000065: 00                                        ; i64 literal
0000066: 3e                                        ; i64.store32
0000067: 01                                        ; alignment
0000068: 00                                        ; store offset
0000069: 41                                        ; i32.const
000006a: 00                                        ; i32 literal
000006b: 42                                        ; i64.const
000006c: 00                                        ; i64 literal
000006d: 3e                                        ; i64.store32
000006e: 02                                        ; alignment
000006f: 00                                        ; store offset
0000070: 41                                        ; i32.const
0000071: 00                                        ; i32 literal
0000072: 42                                        ; i64.const
0000073: 00                                        ; i64 literal
0000074: 37                                        ; i64.store
0000075: 00                                        ; alignment
0000076: 00                                        ; store offset
0000077: 41                                        ; i32.const
0000078: 00                                        ; i32 literal
0000079: 42                                        ; i64.const
000007a: 00                                        ; i64 literal
000007b: 37                                        ; i64.store
000007c: 01                                        ; alignment
000007d: 00                                        ; store offset
000007e: 41                                        ; i32.const
000007f: 00                                        ; i32 literal
0000080: 42                                        ; i64.const
0000081: 00                                        ; i64 literal
0000082: 37                                        ; i64.store
0000083: 02                                        ; alignment
0000084: 00                                        ; store offset
0000085: 41                                        ; i32.const
0000086: 00                                        ; i32 literal
0000087: 42                                        ; i64.const
0000088: 00                                        ; i64 literal
0000089: 37                                        ; i64.store
000008a: 03                                        ; alignment
000008b: 00                                        ; store offset
000008c: 0b                                        ; end
000001a: 72                                        ; FIXUP func body size
0000018: 74                                        ; FIXUP section size
;;; STDERR ;;)
(;; STDOUT ;;;

store-aligned.wasm:	file format wasm 0x1

Code Disassembly:

00001b func[0]:
 00001c: 41 00                      | i32.const 0
 00001e: 41 00                      | i32.const 0
 000020: 3a 00 00                   | i32.store8 0 0
 000023: 41 00                      | i32.const 0
 000025: 41 00                      | i32.const 0
 000027: 3b 00 00                   | i32.store16 0 0
 00002a: 41 00                      | i32.const 0
 00002c: 41 00                      | i32.const 0
 00002e: 3b 01 00                   | i32.store16 1 0
 000031: 41 00                      | i32.const 0
 000033: 41 00                      | i32.const 0
 000035: 36 00 00                   | i32.store 0 0
 000038: 41 00                      | i32.const 0
 00003a: 41 00                      | i32.const 0
 00003c: 36 01 00                   | i32.store 1 0
 00003f: 41 00                      | i32.const 0
 000041: 41 00                      | i32.const 0
 000043: 36 02 00                   | i32.store 2 0
 000046: 41 00                      | i32.const 0
 000048: 42 00                      | i64.const 0
 00004a: 3c 00 00                   | i64.store8 0 0
 00004d: 41 00                      | i32.const 0
 00004f: 42 00                      | i64.const 0
 000051: 3d 00 00                   | i64.store16 0 0
 000054: 41 00                      | i32.const 0
 000056: 42 00                      | i64.const 0
 000058: 3d 01 00                   | i64.store16 1 0
 00005b: 41 00                      | i32.const 0
 00005d: 42 00                      | i64.const 0
 00005f: 3e 00 00                   | i64.store32 0 0
 000062: 41 00                      | i32.const 0
 000064: 42 00                      | i64.const 0
 000066: 3e 01 00                   | i64.store32 1 0
 000069: 41 00                      | i32.const 0
 00006b: 42 00                      | i64.const 0
 00006d: 3e 02 00                   | i64.store32 2 0
 000070: 41 00                      | i32.const 0
 000072: 42 00                      | i64.const 0
 000074: 37 00 00                   | i64.store 0 0
 000077: 41 00                      | i32.const 0
 000079: 42 00                      | i64.const 0
 00007b: 37 01 00                   | i64.store 1 0
 00007e: 41 00                      | i32.const 0
 000080: 42 00                      | i64.const 0
 000082: 37 02 00                   | i64.store 2 0
 000085: 41 00                      | i32.const 0
 000087: 42 00                      | i64.const 0
 000089: 37 03 00                   | i64.store 3 0
 00008c: 0b                         | end
;;; STDOUT ;;)
